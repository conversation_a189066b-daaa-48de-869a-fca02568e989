# 🚀 Deploy Printily NOW!

## ⚡ **Quick Deployment (5 minutes)**

### **Option 1: Docker Deployment (Easiest)**

```bash
# 1. Clone the repository
git clone https://github.com/nadjib-dbz/printily.git
cd printily

# 2. Run deployment script
./deploy.sh

# 3. Access your app
# Frontend: http://localhost:3000
# Backend: http://localhost:8000
# Full App: http://localhost
```

### **Option 2: Development Deployment**

```bash
# 1. Clone repository
git clone https://github.com/nadjib-dbz/printily.git
cd printily

# 2. Start Backend
cd backend
composer install
cp .env.example .env
php artisan key:generate
php artisan serve &

# 3. Start Frontend (new terminal)
cd ../frontend
npm install
npm run dev

# 4. Access your app
# Frontend: http://localhost:5173
# Backend: http://localhost:8000
```

---

## 🌐 **Cloud Deployment Options**

### **Heroku Deployment**

1. **Create Heroku apps**
```bash
# Backend
heroku create printily-api
heroku config:set APP_KEY=$(php artisan key:generate --show) -a printily-api

# Frontend
heroku create printily-app
```

2. **Deploy backend**
```bash
cd backend
git init
heroku git:remote -a printily-api
git add .
git commit -m "Deploy backend"
git push heroku main
```

3. **Deploy frontend**
```bash
cd ../frontend
echo "VITE_API_URL=https://printily-api.herokuapp.com/api/v1" > .env.production
npm run build
# Deploy dist folder to Heroku or Netlify
```

### **DigitalOcean Deployment**

1. **Create Droplet** (Ubuntu 20.04, 2GB RAM minimum)

2. **SSH into server**
```bash
ssh root@your-server-ip
```

3. **Install Docker**
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
```

4. **Deploy application**
```bash
git clone https://github.com/nadjib-dbz/printily.git
cd printily
./deploy.sh
```

### **AWS/VPS Deployment**

1. **Launch EC2 instance** or VPS
2. **Install dependencies**
3. **Clone and deploy**
```bash
git clone https://github.com/nadjib-dbz/printily.git
cd printily
./deploy.sh
```

---

## 🔧 **Production Configuration**

### **Environment Setup**

1. **Backend (.env)**
```bash
cp backend/.env.production backend/.env
# Edit with your domain and settings
```

2. **Frontend (.env.production)**
```bash
cp frontend/.env.production frontend/.env
# Update API URL and domain
```

### **SSL Certificate (Let's Encrypt)**

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### **Performance Optimization**

1. **Enable caching**
```bash
# Laravel
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Nginx
# Add to nginx.conf:
# gzip on;
# expires 1y;
```

2. **Database optimization**
```bash
# If using MySQL
mysql -u root -p
CREATE DATABASE printily;
# Run migrations
php artisan migrate --force
```

---

## 📊 **Monitoring & Maintenance**

### **Health Checks**

```bash
# Check services
curl http://localhost:8000/api/v1/products
curl http://localhost:3000

# Docker status
docker-compose ps
docker-compose logs -f
```

### **Backup Script**

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf printily_backup_$DATE.tar.gz .
# Upload to cloud storage
```

### **Update Deployment**

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

---

## 🎯 **Deployment Checklist**

### **Pre-deployment**
- [ ] Domain name configured
- [ ] SSL certificate ready
- [ ] Server resources adequate (2GB+ RAM)
- [ ] Backup strategy in place

### **Deployment**
- [ ] Repository cloned
- [ ] Environment variables configured
- [ ] Services running
- [ ] Health checks passing

### **Post-deployment**
- [ ] Frontend accessible
- [ ] API responding
- [ ] Design editor working
- [ ] Product catalog loading
- [ ] Authentication functional

### **Production Ready**
- [ ] SSL certificate installed
- [ ] Monitoring set up
- [ ] Backup automated
- [ ] Performance optimized
- [ ] Security hardened

---

## 🆘 **Troubleshooting**

### **Common Issues**

1. **Port conflicts**
```bash
# Check what's using ports
sudo netstat -tulpn | grep :80
sudo netstat -tulpn | grep :8000
```

2. **Permission errors**
```bash
# Fix Laravel permissions
sudo chown -R www-data:www-data backend/storage
sudo chmod -R 775 backend/storage
```

3. **Docker issues**
```bash
# Restart Docker
sudo systemctl restart docker
docker-compose down && docker-compose up -d
```

### **Support**

- **Documentation**: Check DEPLOYMENT_GUIDE.md
- **Issues**: https://github.com/nadjib-dbz/printily/issues
- **Logs**: `docker-compose logs -f`

---

## 🎉 **You're Ready!**

Your **Printily platform** is now deployed and ready for users! 

**Next steps:**
1. **Test all features** - Products, customization, authentication
2. **Configure domain** - Point your domain to the server
3. **Set up monitoring** - Track performance and errors
4. **Launch marketing** - Start promoting your platform

**Welcome to the print-on-demand business!** 🇩🇿✨
