@import "tailwindcss";

@theme {
  --color-primary-50: #fef2f2;
  --color-primary-100: #fee2e2;
  --color-primary-200: #fecaca;
  --color-primary-300: #fca5a5;
  --color-primary-400: #f87171;
  --color-primary-500: #ef4444;
  --color-primary-600: #dc2626;
  --color-primary-700: #b91c1c;
  --color-primary-800: #991b1b;
  --color-primary-900: #7f1d1d;

  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;

  --color-accent-50: #fefce8;
  --color-accent-100: #fef9c3;
  --color-accent-200: #fef08a;
  --color-accent-300: #fde047;
  --color-accent-400: #facc15;
  --color-accent-500: #eab308;
  --color-accent-600: #ca8a04;
  --color-accent-700: #a16207;
  --color-accent-800: #854d0e;
  --color-accent-900: #713f12;

  --font-family-sans: 'Inter', system-ui, sans-serif;
  --font-family-display: 'Poppins', system-ui, sans-serif;
}

/* Base styles */
html {
  font-family: var(--font-family-sans);
}

body {
  background-color: rgb(249 250 251);
  color: rgb(17 24 39);
}

/* Component styles */
.btn-primary {
  background-color: var(--color-primary-500);
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: var(--color-primary-600);
}

.btn-secondary {
  background-color: var(--color-secondary-100);
  color: var(--color-secondary-700);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: var(--color-secondary-200);
}

.btn-outline {
  border: 1px solid var(--color-primary-500);
  color: var(--color-primary-500);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
}

.btn-outline:hover {
  background-color: var(--color-primary-500);
  color: white;
}

.card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  border: 1px solid rgb(229 231 235);
  padding: 1.5rem;
}

.input-field {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgb(209 213 219);
  border-radius: 0.5rem;
  outline: none;
}

.input-field:focus {
  ring: 2px;
  ring-color: var(--color-primary-500);
  border-color: transparent;
}

.page-container {
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .page-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .page-container {
    padding: 0 2rem;
  }
}

.section-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: rgb(17 24 39);
  margin-bottom: 2rem;
  font-family: var(--font-family-display);
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .product-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .product-grid {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.text-gradient {
  background: linear-gradient(to right, var(--color-primary-500), var(--color-accent-500));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}
