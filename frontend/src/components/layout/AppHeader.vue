<template>
  <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
    <div class="page-container">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <RouterLink to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">P</span>
            </div>
            <span class="text-2xl font-bold text-gradient font-display">Printily</span>
          </RouterLink>
        </div>

        <!-- Navigation -->
        <nav class="hidden md:flex items-center space-x-8">
          <RouterLink 
            to="/" 
            class="text-gray-700 hover:text-primary-500 font-medium transition-colors"
            active-class="text-primary-500"
          >
            Home
          </RouterLink>
          <RouterLink 
            to="/products" 
            class="text-gray-700 hover:text-primary-500 font-medium transition-colors"
            active-class="text-primary-500"
          >
            Products
          </RouterLink>
          <RouterLink 
            to="/designs" 
            class="text-gray-700 hover:text-primary-500 font-medium transition-colors"
            active-class="text-primary-500"
          >
            Designs
          </RouterLink>
          <RouterLink 
            to="/about" 
            class="text-gray-700 hover:text-primary-500 font-medium transition-colors"
            active-class="text-primary-500"
          >
            About
          </RouterLink>
        </nav>

        <!-- User Menu -->
        <div class="flex items-center space-x-4">
          <!-- Search -->
          <div class="hidden lg:block">
            <div class="relative">
              <input
                type="text"
                placeholder="Search products..."
                class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                v-model="searchQuery"
                @keyup.enter="handleSearch"
              >
              <MagnifyingGlassIcon class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
          </div>

          <!-- Auth Buttons / User Menu -->
          <div v-if="!authStore.isAuthenticated" class="flex items-center space-x-2">
            <RouterLink to="/login" class="btn-outline">
              Login
            </RouterLink>
            <RouterLink to="/register" class="btn-primary">
              Sign Up
            </RouterLink>
          </div>

          <div v-else class="relative">
            <Menu as="div" class="relative">
              <MenuButton class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors">
                <div class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-medium">
                    {{ authStore.user?.name?.charAt(0).toUpperCase() }}
                  </span>
                </div>
                <ChevronDownIcon class="h-4 w-4 text-gray-500" />
              </MenuButton>

              <transition
                enter-active-class="transition duration-100 ease-out"
                enter-from-class="transform scale-95 opacity-0"
                enter-to-class="transform scale-100 opacity-100"
                leave-active-class="transition duration-75 ease-in"
                leave-from-class="transform scale-100 opacity-100"
                leave-to-class="transform scale-95 opacity-0"
              >
                <MenuItems class="absolute right-0 mt-2 w-56 origin-top-right bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <div class="p-1">
                    <MenuItem v-slot="{ active }">
                      <RouterLink
                        to="/dashboard"
                        :class="[
                          active ? 'bg-gray-100' : '',
                          'flex items-center px-3 py-2 text-sm text-gray-700 rounded-md'
                        ]"
                      >
                        <UserIcon class="mr-3 h-4 w-4" />
                        Dashboard
                      </RouterLink>
                    </MenuItem>
                    <MenuItem v-slot="{ active }">
                      <RouterLink
                        to="/dashboard/designs"
                        :class="[
                          active ? 'bg-gray-100' : '',
                          'flex items-center px-3 py-2 text-sm text-gray-700 rounded-md'
                        ]"
                      >
                        <PaintBrushIcon class="mr-3 h-4 w-4" />
                        My Designs
                      </RouterLink>
                    </MenuItem>
                    <MenuItem v-slot="{ active }">
                      <RouterLink
                        to="/dashboard/orders"
                        :class="[
                          active ? 'bg-gray-100' : '',
                          'flex items-center px-3 py-2 text-sm text-gray-700 rounded-md'
                        ]"
                      >
                        <ShoppingBagIcon class="mr-3 h-4 w-4" />
                        My Orders
                      </RouterLink>
                    </MenuItem>
                    <MenuItem v-if="authStore.isAdmin" v-slot="{ active }">
                      <RouterLink
                        to="/admin"
                        :class="[
                          active ? 'bg-gray-100' : '',
                          'flex items-center px-3 py-2 text-sm text-gray-700 rounded-md'
                        ]"
                      >
                        <CogIcon class="mr-3 h-4 w-4" />
                        Admin Panel
                      </RouterLink>
                    </MenuItem>
                    <div class="border-t border-gray-100 my-1"></div>
                    <MenuItem v-slot="{ active }">
                      <button
                        @click="handleLogout"
                        :class="[
                          active ? 'bg-gray-100' : '',
                          'flex w-full items-center px-3 py-2 text-sm text-gray-700 rounded-md'
                        ]"
                      >
                        <ArrowRightOnRectangleIcon class="mr-3 h-4 w-4" />
                        Logout
                      </button>
                    </MenuItem>
                  </div>
                </MenuItems>
              </transition>
            </Menu>
          </div>

          <!-- Mobile menu button -->
          <button
            @click="mobileMenuOpen = !mobileMenuOpen"
            class="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Bars3Icon v-if="!mobileMenuOpen" class="h-6 w-6" />
            <XMarkIcon v-else class="h-6 w-6" />
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div v-if="mobileMenuOpen" class="md:hidden border-t border-gray-200 py-4">
        <nav class="flex flex-col space-y-2">
          <RouterLink 
            to="/" 
            class="px-3 py-2 text-gray-700 hover:text-primary-500 font-medium transition-colors rounded-lg"
            active-class="text-primary-500 bg-primary-50"
            @click="mobileMenuOpen = false"
          >
            Home
          </RouterLink>
          <RouterLink 
            to="/products" 
            class="px-3 py-2 text-gray-700 hover:text-primary-500 font-medium transition-colors rounded-lg"
            active-class="text-primary-500 bg-primary-50"
            @click="mobileMenuOpen = false"
          >
            Products
          </RouterLink>
          <RouterLink 
            to="/designs" 
            class="px-3 py-2 text-gray-700 hover:text-primary-500 font-medium transition-colors rounded-lg"
            active-class="text-primary-500 bg-primary-50"
            @click="mobileMenuOpen = false"
          >
            Designs
          </RouterLink>
          <RouterLink 
            to="/about" 
            class="px-3 py-2 text-gray-700 hover:text-primary-500 font-medium transition-colors rounded-lg"
            active-class="text-primary-500 bg-primary-50"
            @click="mobileMenuOpen = false"
          >
            About
          </RouterLink>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'
import {
  MagnifyingGlassIcon,
  UserIcon,
  PaintBrushIcon,
  ShoppingBagIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
const router = useRouter()

const searchQuery = ref('')
const mobileMenuOpen = ref(false)

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({ name: 'products', query: { search: searchQuery.value } })
  }
}

const handleLogout = async () => {
  await authStore.logout()
  router.push({ name: 'home' })
}
</script>
