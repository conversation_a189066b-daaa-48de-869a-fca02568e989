<template>
  <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
    <div class="page-container">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <RouterLink to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">P</span>
            </div>
            <span class="text-2xl font-bold text-gradient font-display">Printily</span>
          </RouterLink>
        </div>

        <!-- Navigation -->
        <nav class="hidden md:flex items-center space-x-8">
          <RouterLink 
            to="/" 
            class="text-gray-700 hover:text-primary-500 font-medium transition-colors"
            active-class="text-primary-500"
          >
            Home
          </RouterLink>
          <RouterLink 
            to="/products" 
            class="text-gray-700 hover:text-primary-500 font-medium transition-colors"
            active-class="text-primary-500"
          >
            Products
          </RouterLink>
          <RouterLink 
            to="/designs" 
            class="text-gray-700 hover:text-primary-500 font-medium transition-colors"
            active-class="text-primary-500"
          >
            Designs
          </RouterLink>
          <RouterLink 
            to="/about" 
            class="text-gray-700 hover:text-primary-500 font-medium transition-colors"
            active-class="text-primary-500"
          >
            About
          </RouterLink>
        </nav>

        <!-- User Menu -->
        <div class="flex items-center space-x-4">
          <!-- Search -->
          <div class="hidden lg:block">
            <div class="relative">
              <input
                type="text"
                placeholder="Search products..."
                class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                v-model="searchQuery"
                @keyup.enter="handleSearch"
              >
              <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
              </svg>
            </div>
          </div>

          <!-- Auth Buttons / User Menu -->
          <div v-if="!authStore.isAuthenticated" class="flex items-center space-x-2">
            <RouterLink to="/login" class="btn-outline">
              Login
            </RouterLink>
            <RouterLink to="/register" class="btn-primary">
              Sign Up
            </RouterLink>
          </div>

          <div v-else class="relative">
            <Menu as="div" class="relative">
              <MenuButton class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors">
                <div class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-medium">
                    {{ authStore.user?.name?.charAt(0).toUpperCase() }}
                  </span>
                </div>
                <svg class="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                </svg>
              </MenuButton>

              <transition
                enter-active-class="transition duration-100 ease-out"
                enter-from-class="transform scale-95 opacity-0"
                enter-to-class="transform scale-100 opacity-100"
                leave-active-class="transition duration-75 ease-in"
                leave-from-class="transform scale-100 opacity-100"
                leave-to-class="transform scale-95 opacity-0"
              >
                <MenuItems class="absolute right-0 mt-2 w-56 origin-top-right bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <div class="p-1">
                    <MenuItem v-slot="{ active }">
                      <RouterLink
                        to="/dashboard"
                        :class="[
                          active ? 'bg-gray-100' : '',
                          'flex items-center px-3 py-2 text-sm text-gray-700 rounded-md'
                        ]"
                      >
                        <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Dashboard
                      </RouterLink>
                    </MenuItem>
                    <MenuItem v-slot="{ active }">
                      <RouterLink
                        to="/dashboard/designs"
                        :class="[
                          active ? 'bg-gray-100' : '',
                          'flex items-center px-3 py-2 text-sm text-gray-700 rounded-md'
                        ]"
                      >
                        <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4a2 2 0 012-2z" />
                        </svg>
                        My Designs
                      </RouterLink>
                    </MenuItem>
                    <MenuItem v-slot="{ active }">
                      <RouterLink
                        to="/dashboard/orders"
                        :class="[
                          active ? 'bg-gray-100' : '',
                          'flex items-center px-3 py-2 text-sm text-gray-700 rounded-md'
                        ]"
                      >
                        <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M8 11v6a2 2 0 002 2h4a2 2 0 002-2v-6M8 11h8" />
                        </svg>
                        My Orders
                      </RouterLink>
                    </MenuItem>
                    <MenuItem v-if="authStore.isAdmin" v-slot="{ active }">
                      <RouterLink
                        to="/admin"
                        :class="[
                          active ? 'bg-gray-100' : '',
                          'flex items-center px-3 py-2 text-sm text-gray-700 rounded-md'
                        ]"
                      >
                        <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        Admin Panel
                      </RouterLink>
                    </MenuItem>
                    <div class="border-t border-gray-100 my-1"></div>
                    <MenuItem v-slot="{ active }">
                      <button
                        @click="handleLogout"
                        :class="[
                          active ? 'bg-gray-100' : '',
                          'flex w-full items-center px-3 py-2 text-sm text-gray-700 rounded-md'
                        ]"
                      >
                        <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        Logout
                      </button>
                    </MenuItem>
                  </div>
                </MenuItems>
              </transition>
            </Menu>
          </div>

          <!-- Mobile menu button -->
          <button
            @click="mobileMenuOpen = !mobileMenuOpen"
            class="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <svg v-if="!mobileMenuOpen" class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
            <svg v-else class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div v-if="mobileMenuOpen" class="md:hidden border-t border-gray-200 py-4">
        <nav class="flex flex-col space-y-2">
          <RouterLink 
            to="/" 
            class="px-3 py-2 text-gray-700 hover:text-primary-500 font-medium transition-colors rounded-lg"
            active-class="text-primary-500 bg-primary-50"
            @click="mobileMenuOpen = false"
          >
            Home
          </RouterLink>
          <RouterLink 
            to="/products" 
            class="px-3 py-2 text-gray-700 hover:text-primary-500 font-medium transition-colors rounded-lg"
            active-class="text-primary-500 bg-primary-50"
            @click="mobileMenuOpen = false"
          >
            Products
          </RouterLink>
          <RouterLink 
            to="/designs" 
            class="px-3 py-2 text-gray-700 hover:text-primary-500 font-medium transition-colors rounded-lg"
            active-class="text-primary-500 bg-primary-50"
            @click="mobileMenuOpen = false"
          >
            Designs
          </RouterLink>
          <RouterLink 
            to="/about" 
            class="px-3 py-2 text-gray-700 hover:text-primary-500 font-medium transition-colors rounded-lg"
            active-class="text-primary-500 bg-primary-50"
            @click="mobileMenuOpen = false"
          >
            About
          </RouterLink>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
const router = useRouter()

const searchQuery = ref('')
const mobileMenuOpen = ref(false)

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({ name: 'products', query: { search: searchQuery.value } })
  }
}

const handleLogout = async () => {
  await authStore.logout()
  router.push({ name: 'home' })
}
</script>
