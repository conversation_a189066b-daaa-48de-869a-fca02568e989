<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-900">{{ title }}</h3>
      <div class="flex items-center space-x-2">
        <select
          v-model="selectedPeriod"
          @change="updateChart"
          class="text-sm border border-gray-300 rounded px-3 py-1 focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="90d">Last 90 days</option>
          <option value="1y">Last year</option>
        </select>
      </div>
    </div>

    <!-- Chart Container -->
    <div class="relative">
      <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
      </div>
      
      <!-- SVG Chart -->
      <div class="h-64 w-full">
        <svg
          ref="chartSvg"
          class="w-full h-full"
          viewBox="0 0 800 300"
          preserveAspectRatio="xMidYMid meet"
        >
          <!-- Grid Lines -->
          <defs>
            <pattern id="grid" width="80" height="30" patternUnits="userSpaceOnUse">
              <path d="M 80 0 L 0 0 0 30" fill="none" stroke="#f3f4f6" stroke-width="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          <!-- Y-axis labels -->
          <g v-for="(tick, index) in yAxisTicks" :key="`y-${index}`">
            <text
              :x="40"
              :y="280 - (index * 60)"
              text-anchor="end"
              class="text-xs fill-gray-500"
            >
              {{ formatYAxisValue(tick) }}
            </text>
            <line
              :x1="50"
              :x2="750"
              :y1="280 - (index * 60)"
              :y2="280 - (index * 60)"
              stroke="#e5e7eb"
              stroke-width="1"
            />
          </g>

          <!-- Chart Line -->
          <path
            v-if="chartPath"
            :d="chartPath"
            fill="none"
            :stroke="chartColor"
            stroke-width="3"
            stroke-linecap="round"
            stroke-linejoin="round"
          />

          <!-- Chart Area Fill -->
          <path
            v-if="chartAreaPath"
            :d="chartAreaPath"
            :fill="`url(#gradient-${chartType})`"
            opacity="0.1"
          />

          <!-- Data Points -->
          <g v-for="(point, index) in chartPoints" :key="`point-${index}`">
            <circle
              :cx="point.x"
              :cy="point.y"
              r="4"
              :fill="chartColor"
              class="hover:r-6 transition-all cursor-pointer"
              @mouseenter="showTooltip(point, $event)"
              @mouseleave="hideTooltip"
            />
          </g>

          <!-- X-axis labels -->
          <g v-for="(label, index) in xAxisLabels" :key="`x-${index}`">
            <text
              :x="50 + (index * (700 / (xAxisLabels.length - 1)))"
              y="295"
              text-anchor="middle"
              class="text-xs fill-gray-500"
            >
              {{ label }}
            </text>
          </g>

          <!-- Gradient Definitions -->
          <defs>
            <linearGradient :id="`gradient-${chartType}`" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" :stop-color="chartColor" stop-opacity="0.3"/>
              <stop offset="100%" :stop-color="chartColor" stop-opacity="0"/>
            </linearGradient>
          </defs>
        </svg>
      </div>

      <!-- Tooltip -->
      <div
        v-if="tooltip.show"
        :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
        class="absolute z-20 bg-gray-900 text-white text-sm rounded-lg px-3 py-2 pointer-events-none transform -translate-x-1/2 -translate-y-full"
      >
        <div class="font-medium">{{ tooltip.label }}</div>
        <div>{{ formatTooltipValue(tooltip.value) }}</div>
        <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
      </div>
    </div>

    <!-- Chart Summary -->
    <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="text-center">
        <div class="text-2xl font-bold text-gray-900">{{ formatSummaryValue(summary.total) }}</div>
        <div class="text-sm text-gray-500">Total</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-green-600">{{ formatSummaryValue(summary.growth) }}</div>
        <div class="text-sm text-gray-500">Growth</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-blue-600">{{ formatSummaryValue(summary.average) }}</div>
        <div class="text-sm text-gray-500">Average</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-purple-600">{{ formatSummaryValue(summary.peak) }}</div>
        <div class="text-sm text-gray-500">Peak</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

interface ChartProps {
  title: string
  data: Array<{ date: string; value: number; label?: string }>
  chartType: 'sales' | 'orders' | 'users' | 'revenue'
  color?: string
}

const props = withDefaults(defineProps<ChartProps>(), {
  color: '#3b82f6'
})

const selectedPeriod = ref('7d')
const isLoading = ref(false)
const chartSvg = ref<SVGElement>()

const tooltip = ref({
  show: false,
  x: 0,
  y: 0,
  label: '',
  value: 0
})

const chartColor = computed(() => {
  const colors = {
    sales: '#10b981',
    orders: '#3b82f6',
    users: '#8b5cf6',
    revenue: '#f59e0b'
  }
  return props.color || colors[props.chartType] || '#3b82f6'
})

const maxValue = computed(() => Math.max(...props.data.map(d => d.value)))
const minValue = computed(() => Math.min(...props.data.map(d => d.value)))

const yAxisTicks = computed(() => {
  const max = maxValue.value
  const ticks = []
  for (let i = 0; i <= 4; i++) {
    ticks.push((max / 4) * i)
  }
  return ticks.reverse()
})

const xAxisLabels = computed(() => {
  return props.data.map(d => {
    const date = new Date(d.date)
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  })
})

const chartPoints = computed(() => {
  const points = []
  const width = 700
  const height = 240
  const padding = 50

  props.data.forEach((item, index) => {
    const x = padding + (index * (width / (props.data.length - 1)))
    const y = height - ((item.value / maxValue.value) * height) + 20
    
    points.push({
      x,
      y,
      value: item.value,
      label: item.label || item.date,
      date: item.date
    })
  })

  return points
})

const chartPath = computed(() => {
  if (chartPoints.value.length === 0) return ''
  
  let path = `M ${chartPoints.value[0].x} ${chartPoints.value[0].y}`
  
  for (let i = 1; i < chartPoints.value.length; i++) {
    const point = chartPoints.value[i]
    path += ` L ${point.x} ${point.y}`
  }
  
  return path
})

const chartAreaPath = computed(() => {
  if (chartPoints.value.length === 0) return ''
  
  let path = `M ${chartPoints.value[0].x} 280`
  path += ` L ${chartPoints.value[0].x} ${chartPoints.value[0].y}`
  
  for (let i = 1; i < chartPoints.value.length; i++) {
    const point = chartPoints.value[i]
    path += ` L ${point.x} ${point.y}`
  }
  
  path += ` L ${chartPoints.value[chartPoints.value.length - 1].x} 280 Z`
  
  return path
})

const summary = computed(() => {
  const values = props.data.map(d => d.value)
  const total = values.reduce((sum, val) => sum + val, 0)
  const average = total / values.length
  const peak = Math.max(...values)
  
  // Calculate growth (last value vs first value)
  const growth = values.length > 1 
    ? ((values[values.length - 1] - values[0]) / values[0]) * 100
    : 0

  return { total, average, peak, growth }
})

const formatYAxisValue = (value: number) => {
  if (props.chartType === 'revenue') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value)
  }
  
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}k`
  }
  
  return value.toString()
}

const formatTooltipValue = (value: number) => {
  if (props.chartType === 'revenue') {
    return new Intl.NumberFormat('ar-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0
    }).format(value)
  }
  
  return value.toLocaleString()
}

const formatSummaryValue = (value: number) => {
  if (props.chartType === 'revenue') {
    return new Intl.NumberFormat('ar-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value)
  }
  
  if (props.chartType === 'growth') {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }
  
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}k`
  }
  
  return Math.round(value).toLocaleString()
}

const showTooltip = (point: any, event: MouseEvent) => {
  const rect = chartSvg.value?.getBoundingClientRect()
  if (rect) {
    tooltip.value = {
      show: true,
      x: point.x,
      y: point.y - 10,
      label: point.label,
      value: point.value
    }
  }
}

const hideTooltip = () => {
  tooltip.value.show = false
}

const updateChart = async () => {
  isLoading.value = true
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 500))
  isLoading.value = false
}

onMounted(() => {
  // Initialize chart
})
</script>
