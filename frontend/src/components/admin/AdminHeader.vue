<template>
  <header class="bg-white shadow-sm border-b border-gray-200">
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <!-- Left side - Logo and Title -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-3">
            <div class="h-8 w-8 bg-primary-500 rounded-lg flex items-center justify-center">
              <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div>
              <h1 class="text-xl font-bold text-gray-900">Printily Admin</h1>
              <p class="text-sm text-gray-500">Management Dashboard</p>
            </div>
          </div>
        </div>

        <!-- Right side - User menu and actions -->
        <div class="flex items-center space-x-4">
          <!-- Notifications -->
          <div class="relative" ref="notificationsRef">
            <button
              @click="showNotifications = !showNotifications"
              class="relative p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25a2.25 2.25 0 0 0 2.25 2.25H21a.75.75 0 0 1 0 1.5H3a.75.75 0 0 1 0-1.5h2.25A2.25 2.25 0 0 0 7.5 12V9.75a6 6 0 0 1 6-6Z" />
              </svg>
              <span
                v-if="notificationsStore.unreadCount > 0"
                class="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
              >
                {{ notificationsStore.unreadCount > 9 ? '9+' : notificationsStore.unreadCount }}
              </span>
            </button>

            <!-- Notifications Dropdown -->
            <div
              v-if="showNotifications"
              class="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden"
            >
              <!-- Header -->
              <div class="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
                <div class="flex items-center space-x-2">
                  <button
                    v-if="notificationsStore.unreadCount > 0"
                    @click="notificationsStore.markAllAsRead()"
                    class="text-sm text-primary-600 hover:text-primary-800"
                  >
                    Mark all read
                  </button>
                  <button
                    @click="showNotifications = false"
                    class="text-gray-400 hover:text-gray-600"
                  >
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Notifications List -->
              <div class="max-h-80 overflow-y-auto">
                <div v-if="notificationsStore.recentNotifications.length === 0" class="p-4 text-center text-gray-500">
                  No notifications
                </div>
                <div v-else>
                  <div
                    v-for="notification in notificationsStore.recentNotifications"
                    :key="notification.id"
                    @click="handleNotificationClick(notification)"
                    :class="[
                      'px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors',
                      !notification.read ? 'bg-blue-50' : ''
                    ]"
                  >
                    <div class="flex items-start space-x-3">
                      <!-- Notification Icon -->
                      <div :class="[
                        'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',
                        getNotificationIconClass(notification.type)
                      ]">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            :d="getNotificationIconPath(notification.type)"
                          />
                        </svg>
                      </div>

                      <!-- Notification Content -->
                      <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between">
                          <p class="text-sm font-medium text-gray-900 truncate">
                            {{ notification.title }}
                          </p>
                          <div class="flex items-center space-x-1">
                            <span v-if="!notification.read" class="w-2 h-2 bg-blue-500 rounded-full"></span>
                            <span class="text-xs text-gray-500">
                              {{ formatNotificationTime(notification.created_at) }}
                            </span>
                          </div>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">{{ notification.message }}</p>
                        <div v-if="notification.action_text" class="mt-2">
                          <span class="text-xs text-primary-600 font-medium">{{ notification.action_text }} →</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Footer -->
              <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
                <RouterLink
                  to="/admin/notifications"
                  class="text-sm text-primary-600 hover:text-primary-800 font-medium"
                  @click="showNotifications = false"
                >
                  View all notifications
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="hidden md:flex items-center space-x-2">
            <RouterLink 
              to="/admin/products/create" 
              class="btn-primary text-sm py-2 px-3"
            >
              <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              Add Product
            </RouterLink>
          </div>

          <!-- User Menu -->
          <div class="relative" ref="userMenuRef">
            <button 
              @click="showUserMenu = !showUserMenu"
              class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <img 
                :src="adminStore.admin?.avatar" 
                :alt="adminStore.admin?.name"
                class="h-8 w-8 rounded-full"
              >
              <div class="hidden md:block text-left">
                <p class="text-sm font-medium text-gray-900">{{ adminStore.admin?.name }}</p>
                <p class="text-xs text-gray-500">{{ adminStore.admin?.role }}</p>
              </div>
              <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            <!-- User Dropdown Menu -->
            <div 
              v-if="showUserMenu"
              class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
            >
              <RouterLink 
                to="/admin/profile" 
                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                @click="showUserMenu = false"
              >
                <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Profile Settings
              </RouterLink>
              
              <RouterLink 
                to="/admin/settings" 
                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                @click="showUserMenu = false"
              >
                <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Settings
              </RouterLink>

              <div class="border-t border-gray-100 my-1"></div>

              <RouterLink 
                to="/" 
                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                @click="showUserMenu = false"
              >
                <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                View Site
              </RouterLink>

              <button 
                @click="handleLogout"
                class="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50"
              >
                <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter, RouterLink } from 'vue-router'
import { useAdminStore } from '@/stores/admin'
import { useAdminNotificationsStore } from '@/stores/adminNotifications'

const router = useRouter()
const adminStore = useAdminStore()
const notificationsStore = useAdminNotificationsStore()

const showUserMenu = ref(false)
const showNotifications = ref(false)
const userMenuRef = ref<HTMLElement>()
const notificationsRef = ref<HTMLElement>()

const handleLogout = async () => {
  showUserMenu.value = false
  await adminStore.logout()
  router.push('/admin/login')
}

const handleNotificationClick = (notification: any) => {
  // Mark as read
  notificationsStore.markAsRead(notification.id)

  // Navigate to action URL if available
  if (notification.action_url) {
    router.push(notification.action_url)
  }

  // Close notifications dropdown
  showNotifications.value = false
}

const getNotificationIconClass = (type: string) => {
  const classes = {
    order: 'bg-green-100 text-green-600',
    user: 'bg-blue-100 text-blue-600',
    product: 'bg-purple-100 text-purple-600',
    warning: 'bg-yellow-100 text-yellow-600',
    error: 'bg-red-100 text-red-600',
    info: 'bg-gray-100 text-gray-600',
    success: 'bg-green-100 text-green-600'
  }
  return classes[type as keyof typeof classes] || classes.info
}

const getNotificationIconPath = (type: string) => {
  const paths = {
    order: 'M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z',
    user: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
    product: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4',
    warning: 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
    error: 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
    info: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
    success: 'M5 13l4 4L19 7'
  }
  return paths[type as keyof typeof paths] || paths.info
}

const formatNotificationTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`

  return date.toLocaleDateString()
}

// Close menus when clicking outside
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    showUserMenu.value = false
  }
  if (notificationsRef.value && !notificationsRef.value.contains(event.target as Node)) {
    showNotifications.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
