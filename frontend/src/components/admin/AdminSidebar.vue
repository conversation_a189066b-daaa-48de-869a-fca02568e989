<template>
  <aside class="w-64 bg-gray-900 min-h-screen">
    <nav class="mt-5 px-2">
      <div class="space-y-1">
        <!-- Dashboard -->
        <RouterLink
          to="/admin/dashboard"
          :class="[
            'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
            $route.path === '/admin/dashboard' 
              ? 'bg-gray-800 text-white' 
              : 'text-gray-300 hover:bg-gray-700 hover:text-white'
          ]"
        >
          <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
          </svg>
          Dashboard
        </RouterLink>

        <!-- Products -->
        <div>
          <button
            @click="toggleSection('products')"
            :class="[
              'group w-full flex items-center justify-between px-2 py-2 text-sm font-medium rounded-md transition-colors',
              isProductsActive 
                ? 'bg-gray-800 text-white' 
                : 'text-gray-300 hover:bg-gray-700 hover:text-white'
            ]"
          >
            <div class="flex items-center">
              <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
              Products
            </div>
            <svg 
              :class="[
                'h-4 w-4 transition-transform',
                expandedSections.products ? 'rotate-90' : ''
              ]" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
          
          <div v-if="expandedSections.products" class="ml-6 mt-1 space-y-1">
            <RouterLink
              to="/admin/products"
              :class="[
                'group flex items-center px-2 py-2 text-sm rounded-md transition-colors',
                $route.path === '/admin/products' 
                  ? 'bg-gray-800 text-white' 
                  : 'text-gray-400 hover:bg-gray-700 hover:text-white'
              ]"
            >
              All Products
            </RouterLink>
            <RouterLink
              to="/admin/products/create"
              :class="[
                'group flex items-center px-2 py-2 text-sm rounded-md transition-colors',
                $route.path === '/admin/products/create' 
                  ? 'bg-gray-800 text-white' 
                  : 'text-gray-400 hover:bg-gray-700 hover:text-white'
              ]"
            >
              Add Product
            </RouterLink>
            <RouterLink
              to="/admin/categories"
              :class="[
                'group flex items-center px-2 py-2 text-sm rounded-md transition-colors',
                $route.path === '/admin/categories' 
                  ? 'bg-gray-800 text-white' 
                  : 'text-gray-400 hover:bg-gray-700 hover:text-white'
              ]"
            >
              Categories
            </RouterLink>
          </div>
        </div>

        <!-- Orders -->
        <RouterLink
          to="/admin/orders"
          :class="[
            'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
            $route.path.startsWith('/admin/orders') 
              ? 'bg-gray-800 text-white' 
              : 'text-gray-300 hover:bg-gray-700 hover:text-white'
          ]"
        >
          <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
          Orders
          <span v-if="pendingOrdersCount > 0" class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1">
            {{ pendingOrdersCount }}
          </span>
        </RouterLink>

        <!-- Users -->
        <RouterLink
          to="/admin/users"
          :class="[
            'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
            $route.path.startsWith('/admin/users') 
              ? 'bg-gray-800 text-white' 
              : 'text-gray-300 hover:bg-gray-700 hover:text-white'
          ]"
        >
          <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
          Users
        </RouterLink>

        <!-- Designs -->
        <RouterLink
          to="/admin/designs"
          :class="[
            'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
            $route.path.startsWith('/admin/designs') 
              ? 'bg-gray-800 text-white' 
              : 'text-gray-300 hover:bg-gray-700 hover:text-white'
          ]"
        >
          <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4a2 2 0 012-2z" />
          </svg>
          Designs
        </RouterLink>

        <!-- Analytics -->
        <div>
          <button
            @click="toggleSection('analytics')"
            :class="[
              'group w-full flex items-center justify-between px-2 py-2 text-sm font-medium rounded-md transition-colors',
              isAnalyticsActive 
                ? 'bg-gray-800 text-white' 
                : 'text-gray-300 hover:bg-gray-700 hover:text-white'
            ]"
          >
            <div class="flex items-center">
              <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Analytics
            </div>
            <svg 
              :class="[
                'h-4 w-4 transition-transform',
                expandedSections.analytics ? 'rotate-90' : ''
              ]" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
          
          <div v-if="expandedSections.analytics" class="ml-6 mt-1 space-y-1">
            <RouterLink
              to="/admin/analytics/overview"
              :class="[
                'group flex items-center px-2 py-2 text-sm rounded-md transition-colors',
                $route.path === '/admin/analytics/overview' 
                  ? 'bg-gray-800 text-white' 
                  : 'text-gray-400 hover:bg-gray-700 hover:text-white'
              ]"
            >
              Overview
            </RouterLink>
            <RouterLink
              to="/admin/analytics/sales"
              :class="[
                'group flex items-center px-2 py-2 text-sm rounded-md transition-colors',
                $route.path === '/admin/analytics/sales' 
                  ? 'bg-gray-800 text-white' 
                  : 'text-gray-400 hover:bg-gray-700 hover:text-white'
              ]"
            >
              Sales Reports
            </RouterLink>
          </div>
        </div>

        <!-- Settings -->
        <RouterLink
          to="/admin/settings"
          :class="[
            'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
            $route.path.startsWith('/admin/settings') 
              ? 'bg-gray-800 text-white' 
              : 'text-gray-300 hover:bg-gray-700 hover:text-white'
          ]"
        >
          <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          Settings
        </RouterLink>
      </div>
    </nav>
  </aside>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, RouterLink } from 'vue-router'
import { useAdminStore } from '@/stores/admin'

const route = useRoute()
const adminStore = useAdminStore()

const expandedSections = ref({
  products: true,
  analytics: false
})

const isProductsActive = computed(() => 
  route.path.startsWith('/admin/products') || route.path.startsWith('/admin/categories')
)

const isAnalyticsActive = computed(() => 
  route.path.startsWith('/admin/analytics')
)

const pendingOrdersCount = computed(() => 
  adminStore.dashboardStats?.overview.orders_pending || 0
)

const toggleSection = (section: keyof typeof expandedSections.value) => {
  expandedSections.value[section] = !expandedSections.value[section]
}
</script>
