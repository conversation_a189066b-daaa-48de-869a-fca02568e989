<template>
  <div class="print-designer-wrapper">
    <!-- Design Editor Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <button 
            @click="$emit('close')"
            class="flex items-center text-gray-600 hover:text-gray-900"
          >
            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            Back
          </button>
          <div class="h-6 w-px bg-gray-300"></div>
          <h1 class="text-xl font-semibold text-gray-900">
            Design Editor
          </h1>
        </div>
        
        <div class="flex items-center space-x-3">
          <button 
            @click="saveDesign"
            :disabled="isSaving"
            class="btn-outline"
          >
            <span v-if="isSaving">Saving...</span>
            <span v-else>Save Design</span>
          </button>
          <button 
            @click="exportDesign"
            :disabled="isExporting"
            class="btn-primary"
          >
            <span v-if="isExporting">Exporting...</span>
            <span v-else>Export & Order</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Main Editor Area -->
    <div class="flex h-screen">
      <!-- Left Sidebar - Tools -->
      <div class="w-64 bg-gray-50 border-r border-gray-200 overflow-y-auto">
        <div class="p-4">
          <h3 class="text-sm font-medium text-gray-900 mb-4">Design Tools</h3>
          
          <!-- Tool Categories -->
          <div class="space-y-2">
            <button 
              @click="activeTab = 'text'"
              :class="[
                'w-full flex items-center px-3 py-2 text-sm rounded-lg transition-colors',
                activeTab === 'text' ? 'bg-primary-100 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
              ]"
            >
              <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
              </svg>
              Text
            </button>
            
            <button 
              @click="activeTab = 'shapes'"
              :class="[
                'w-full flex items-center px-3 py-2 text-sm rounded-lg transition-colors',
                activeTab === 'shapes' ? 'bg-primary-100 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
              ]"
            >
              <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              Shapes
            </button>
            
            <button 
              @click="activeTab = 'images'"
              :class="[
                'w-full flex items-center px-3 py-2 text-sm rounded-lg transition-colors',
                activeTab === 'images' ? 'bg-primary-100 text-primary-700' : 'text-gray-700 hover:bg-gray-100'
              ]"
            >
              <svg class="h-4 w-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              Images
            </button>
          </div>

          <!-- Tool Content -->
          <div class="mt-6">
            <!-- Text Tools -->
            <div v-if="activeTab === 'text'" class="space-y-4">
              <button 
                @click="addText"
                class="w-full btn-primary text-sm py-2"
              >
                Add Text
              </button>
              
              <div v-if="selectedObject && selectedObject.type === 'text'">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Text Properties</h4>
                
                <div class="space-y-3">
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Font Size</label>
                    <input 
                      type="range" 
                      min="8" 
                      max="72" 
                      v-model="textProperties.fontSize"
                      @input="updateTextProperties"
                      class="w-full"
                    >
                    <span class="text-xs text-gray-500">{{ textProperties.fontSize }}px</span>
                  </div>
                  
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Color</label>
                    <input 
                      type="color" 
                      v-model="textProperties.fill"
                      @input="updateTextProperties"
                      class="w-full h-8 rounded border border-gray-300"
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- Shapes Tools -->
            <div v-if="activeTab === 'shapes'" class="space-y-2">
              <button 
                @click="addShape('rectangle')"
                class="w-full btn-secondary text-sm py-2"
              >
                Rectangle
              </button>
              <button 
                @click="addShape('circle')"
                class="w-full btn-secondary text-sm py-2"
              >
                Circle
              </button>
            </div>

            <!-- Images Tools -->
            <div v-if="activeTab === 'images'" class="space-y-4">
              <input 
                type="file" 
                ref="imageInput"
                @change="handleImageUpload"
                accept="image/*"
                class="hidden"
              >
              <button 
                @click="$refs.imageInput.click()"
                class="w-full btn-secondary text-sm py-2"
              >
                Upload Image
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Center - Canvas Area -->
      <div class="flex-1 bg-gray-100 flex items-center justify-center p-8">
        <div class="bg-white rounded-lg shadow-lg p-6">
          <canvas 
            ref="fabricCanvas"
            :width="canvasWidth"
            :height="canvasHeight"
            class="border border-gray-300 rounded"
          ></canvas>
        </div>
      </div>

      <!-- Right Sidebar - Layers -->
      <div class="w-64 bg-gray-50 border-l border-gray-200 overflow-y-auto">
        <div class="p-4">
          <h3 class="text-sm font-medium text-gray-900 mb-4">Layers</h3>
          
          <div class="space-y-2">
            <div 
              v-for="(layer, index) in layers" 
              :key="layer.id"
              @click="selectLayer(layer)"
              :class="[
                'p-3 rounded-lg border cursor-pointer transition-colors',
                selectedObject && selectedObject.layerId === layer.id 
                  ? 'border-primary-500 bg-primary-50' 
                  : 'border-gray-200 hover:border-gray-300'
              ]"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <svg v-if="layer.type === 'text'" class="h-4 w-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
                  </svg>
                  <svg v-else-if="layer.type === 'image'" class="h-4 w-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <svg v-else class="h-4 w-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  <span class="text-sm">{{ layer.name || `${layer.type} ${index + 1}` }}</span>
                </div>
                <button 
                  @click.stop="deleteLayer(layer)"
                  class="text-gray-400 hover:text-red-500"
                >
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { fabric } from 'fabric'

// Props
const props = defineProps<{
  product?: any
  initialDesign?: any
}>()

// Emits
const emit = defineEmits<{
  close: []
  save: [design: any]
  export: [design: any]
}>()

// Reactive data
const fabricCanvas = ref<HTMLCanvasElement>()
const canvas = ref<fabric.Canvas>()
const activeTab = ref('text')
const selectedObject = ref<fabric.Object | null>(null)
const layers = ref<any[]>([])
const isSaving = ref(false)
const isExporting = ref(false)
const imageInput = ref<HTMLInputElement>()

// Canvas dimensions
const canvasWidth = 400
const canvasHeight = 500

// Text properties
const textProperties = ref({
  fontSize: 24,
  fill: '#000000',
  fontFamily: 'Arial'
})

// Initialize Fabric.js canvas
onMounted(() => {
  if (fabricCanvas.value) {
    canvas.value = new fabric.Canvas(fabricCanvas.value, {
      width: canvasWidth,
      height: canvasHeight,
      backgroundColor: '#ffffff'
    })

    // Set up event listeners
    canvas.value.on('selection:created', handleSelection)
    canvas.value.on('selection:updated', handleSelection)
    canvas.value.on('selection:cleared', () => {
      selectedObject.value = null
    })
    canvas.value.on('object:added', updateLayers)
    canvas.value.on('object:removed', updateLayers)

    // Load initial design if provided
    if (props.initialDesign) {
      loadDesign(props.initialDesign)
    }
  }
})

onUnmounted(() => {
  if (canvas.value) {
    canvas.value.dispose()
  }
})

// Methods
const handleSelection = (e: fabric.IEvent) => {
  selectedObject.value = e.selected?.[0] || null
  if (selectedObject.value && selectedObject.value.type === 'text') {
    const textObj = selectedObject.value as fabric.Text
    textProperties.value.fontSize = textObj.fontSize || 24
    textProperties.value.fill = textObj.fill as string || '#000000'
  }
}

const updateLayers = () => {
  if (canvas.value) {
    layers.value = canvas.value.getObjects().map((obj, index) => ({
      id: (obj as any).layerId || index,
      type: obj.type,
      name: (obj as any).name || `${obj.type} ${index + 1}`,
      object: obj
    }))
  }
}

const addText = () => {
  if (canvas.value) {
    const text = new fabric.Text('Your Text Here', {
      left: canvasWidth / 2,
      top: canvasHeight / 2,
      fontSize: textProperties.value.fontSize,
      fill: textProperties.value.fill,
      originX: 'center',
      originY: 'center'
    })
    
    // Add custom properties
    ;(text as any).layerId = Date.now()
    ;(text as any).name = 'Text Layer'
    
    canvas.value.add(text)
    canvas.value.setActiveObject(text)
    canvas.value.renderAll()
  }
}

const addShape = (shapeType: string) => {
  if (canvas.value) {
    let shape: fabric.Object
    
    if (shapeType === 'rectangle') {
      shape = new fabric.Rect({
        left: canvasWidth / 2,
        top: canvasHeight / 2,
        width: 100,
        height: 100,
        fill: '#ff0000',
        originX: 'center',
        originY: 'center'
      })
    } else if (shapeType === 'circle') {
      shape = new fabric.Circle({
        left: canvasWidth / 2,
        top: canvasHeight / 2,
        radius: 50,
        fill: '#00ff00',
        originX: 'center',
        originY: 'center'
      })
    } else {
      return
    }
    
    // Add custom properties
    ;(shape as any).layerId = Date.now()
    ;(shape as any).name = `${shapeType} Layer`
    
    canvas.value.add(shape)
    canvas.value.setActiveObject(shape)
    canvas.value.renderAll()
  }
}

const handleImageUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file && canvas.value) {
    const reader = new FileReader()
    reader.onload = (e) => {
      const imgUrl = e.target?.result as string
      fabric.Image.fromURL(imgUrl, (img) => {
        img.set({
          left: canvasWidth / 2,
          top: canvasHeight / 2,
          originX: 'center',
          originY: 'center'
        })
        
        // Scale image to fit canvas
        const scale = Math.min(canvasWidth / img.width!, canvasHeight / img.height!) * 0.5
        img.scale(scale)
        
        // Add custom properties
        ;(img as any).layerId = Date.now()
        ;(img as any).name = 'Image Layer'
        
        canvas.value!.add(img)
        canvas.value!.setActiveObject(img)
        canvas.value!.renderAll()
      })
    }
    reader.readAsDataURL(file)
  }
}

const updateTextProperties = () => {
  if (selectedObject.value && selectedObject.value.type === 'text') {
    const textObj = selectedObject.value as fabric.Text
    textObj.set({
      fontSize: textProperties.value.fontSize,
      fill: textProperties.value.fill
    })
    canvas.value?.renderAll()
  }
}

const selectLayer = (layer: any) => {
  if (canvas.value) {
    canvas.value.setActiveObject(layer.object)
    canvas.value.renderAll()
  }
}

const deleteLayer = (layer: any) => {
  if (canvas.value) {
    canvas.value.remove(layer.object)
    canvas.value.renderAll()
  }
}

const saveDesign = async () => {
  if (!canvas.value) return
  
  isSaving.value = true
  try {
    const designData = {
      canvas: canvas.value.toJSON(),
      preview: canvas.value.toDataURL('image/png')
    }
    emit('save', designData)
  } finally {
    isSaving.value = false
  }
}

const exportDesign = async () => {
  if (!canvas.value) return
  
  isExporting.value = true
  try {
    const designData = {
      canvas: canvas.value.toJSON(),
      preview: canvas.value.toDataURL('image/png'),
      export: canvas.value.toDataURL('image/png', { quality: 1.0 })
    }
    emit('export', designData)
  } finally {
    isExporting.value = false
  }
}

const loadDesign = (designData: any) => {
  if (canvas.value && designData.canvas) {
    canvas.value.loadFromJSON(designData.canvas, () => {
      canvas.value!.renderAll()
      updateLayers()
    })
  }
}
</script>

<style scoped>
.print-designer-wrapper {
  height: 100vh;
  overflow: hidden;
}
</style>
