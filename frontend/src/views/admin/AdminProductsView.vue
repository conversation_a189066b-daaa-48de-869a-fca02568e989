<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON><PERSON>er -->
    <AdminHeader />

    <!-- Main Content -->
    <div class="flex">
      <!-- Sidebar -->
      <AdminSidebar />

      <!-- Products Content -->
      <main class="flex-1 p-6">
        <!-- <PERSON> Header -->
        <div class="mb-8">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-gray-900">Products</h1>
              <p class="text-gray-600 mt-2">Manage your product catalog</p>
            </div>
            <div class="flex items-center space-x-3">
              <button @click="exportProducts" class="btn-outline">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export
              </button>
              <RouterLink to="/admin/products/create" class="btn-primary">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Product
              </RouterLink>
            </div>
          </div>
        </div>

        <!-- Product Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="p-3 rounded-full bg-blue-100">
                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Products</p>
                <p class="text-2xl font-bold text-gray-900">{{ adminProductsStore.productStats.total }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="p-3 rounded-full bg-green-100">
                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Active Products</p>
                <p class="text-2xl font-bold text-gray-900">{{ adminProductsStore.productStats.active }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="p-3 rounded-full bg-yellow-100">
                <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(adminProductsStore.productStats.total_revenue) }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="p-3 rounded-full bg-purple-100">
                <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Avg. Price</p>
                <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(adminProductsStore.productStats.avg_price) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Advanced Filters -->
        <div class="bg-white rounded-lg shadow mb-6 p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Search -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Search Products</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  v-model="adminProductsStore.filters.search"
                  type="text"
                  placeholder="Search by name, description..."
                  class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
              </div>
            </div>

            <!-- Category Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
              <select
                v-model="adminProductsStore.filters.category_id"
                class="block w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">All Categories</option>
                <option value="1">T-Shirts</option>
                <option value="2">Mugs</option>
                <option value="3">Hoodies</option>
                <option value="4">Accessories</option>
              </select>
            </div>

            <!-- Status Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                v-model="adminProductsStore.filters.status"
                class="block w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <!-- Sort Options -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
              <select
                v-model="adminProductsStore.filters.sort_by"
                class="block w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="created_at">Date Created</option>
                <option value="name">Name</option>
                <option value="base_price">Price</option>
                <option value="admin_data.total_sales">Sales</option>
              </select>
            </div>
          </div>

          <!-- Price Range Filter -->
          <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Price Range (DZD)</label>
            <div class="flex items-center space-x-4">
              <input
                v-model.number="adminProductsStore.filters.price_range[0]"
                type="number"
                placeholder="Min"
                class="w-24 border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
              <span class="text-gray-500">to</span>
              <input
                v-model.number="adminProductsStore.filters.price_range[1]"
                type="number"
                placeholder="Max"
                class="w-24 border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
              <button @click="resetFilters" class="btn-outline text-sm">
                Reset Filters
              </button>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="adminProductsStore.isLoading" class="flex items-center justify-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>

        <!-- Products Grid/Table -->
        <div v-else class="bg-white rounded-lg shadow overflow-hidden">
          <!-- Table Header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900">
                Products ({{ adminProductsStore.filteredProducts.length }})
              </h3>

              <!-- View Toggle -->
              <div class="flex items-center space-x-4">
                <!-- Bulk Actions -->
                <div v-if="selectedProducts.length > 0" class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600">{{ selectedProducts.length }} selected</span>
                  <button
                    @click="bulkAction('activate')"
                    class="btn-outline text-sm py-1 px-3"
                  >
                    Activate
                  </button>
                  <button
                    @click="bulkAction('deactivate')"
                    class="btn-outline text-sm py-1 px-3"
                  >
                    Deactivate
                  </button>
                  <button
                    @click="bulkAction('delete')"
                    class="bg-red-600 text-white text-sm py-1 px-3 rounded hover:bg-red-700"
                  >
                    Delete
                  </button>
                </div>

                <!-- View Toggle Buttons -->
                <div class="flex items-center bg-gray-100 rounded-lg p-1">
                  <button
                    @click="viewMode = 'table'"
                    :class="[
                      'px-3 py-1 rounded text-sm font-medium transition-colors',
                      viewMode === 'table'
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    ]"
                  >
                    Table
                  </button>
                  <button
                    @click="viewMode = 'grid'"
                    :class="[
                      'px-3 py-1 rounded text-sm font-medium transition-colors',
                      viewMode === 'grid'
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    ]"
                  >
                    Grid
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Table View -->
          <div v-if="viewMode === 'table'" class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      @change="toggleSelectAll"
                      :checked="selectedProducts.length === adminProductsStore.filteredProducts.length && adminProductsStore.filteredProducts.length > 0"
                      class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    >
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="product in paginatedProducts" :key="product.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      :value="product.id"
                      v-model="selectedProducts"
                      class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    >
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <img
                        :src="product.mockup_image"
                        :alt="product.name"
                        class="h-12 w-12 rounded-lg object-cover"
                      >
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                        <div class="text-sm text-gray-500">ID: {{ product.id }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="text-sm text-gray-900">{{ product.category?.name || 'Unknown' }}</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="text-sm font-medium text-gray-900">{{ formatCurrency(product.base_price) }}</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ product.admin_data?.total_sales || 0 }} sales</div>
                    <div class="text-sm text-gray-500">{{ formatCurrency(product.admin_data?.revenue || 0) }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <button
                      @click="toggleProductStatus(product)"
                      :class="[
                        'px-2 inline-flex text-xs leading-5 font-semibold rounded-full transition-colors',
                        product.is_active
                          ? 'bg-green-100 text-green-800 hover:bg-green-200'
                          : 'bg-red-100 text-red-800 hover:bg-red-200'
                      ]"
                    >
                      {{ product.is_active ? 'Active' : 'Inactive' }}
                    </button>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-2">
                      <button
                        @click="editProduct(product)"
                        class="text-primary-600 hover:text-primary-900"
                      >
                        Edit
                      </button>
                      <button
                        @click="duplicateProduct(product)"
                        class="text-gray-600 hover:text-gray-900"
                      >
                        Duplicate
                      </button>
                      <button
                        @click="deleteProduct(product)"
                        class="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Grid View -->
          <div v-else class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              <div
                v-for="product in paginatedProducts"
                :key="product.id"
                class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow"
              >
                <!-- Product Image -->
                <div class="relative">
                  <img
                    :src="product.mockup_image"
                    :alt="product.name"
                    class="w-full h-48 object-cover"
                  >
                  <div class="absolute top-2 left-2">
                    <input
                      type="checkbox"
                      :value="product.id"
                      v-model="selectedProducts"
                      class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    >
                  </div>
                  <div class="absolute top-2 right-2">
                    <button
                      @click="toggleProductStatus(product)"
                      :class="[
                        'px-2 py-1 text-xs font-semibold rounded-full',
                        product.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      ]"
                    >
                      {{ product.is_active ? 'Active' : 'Inactive' }}
                    </button>
                  </div>
                </div>

                <!-- Product Info -->
                <div class="p-4">
                  <h3 class="text-sm font-medium text-gray-900 truncate">{{ product.name }}</h3>
                  <p class="text-sm text-gray-500 mt-1">{{ product.category?.name }}</p>
                  <div class="flex items-center justify-between mt-3">
                    <span class="text-lg font-bold text-gray-900">{{ formatCurrency(product.base_price) }}</span>
                    <span class="text-sm text-gray-500">{{ product.admin_data?.total_sales || 0 }} sales</span>
                  </div>

                  <!-- Actions -->
                  <div class="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                    <button
                      @click="editProduct(product)"
                      class="text-primary-600 hover:text-primary-900 text-sm font-medium"
                    >
                      Edit
                    </button>
                    <button
                      @click="duplicateProduct(product)"
                      class="text-gray-600 hover:text-gray-900 text-sm font-medium"
                    >
                      Duplicate
                    </button>
                    <button
                      @click="deleteProduct(product)"
                      class="text-red-600 hover:text-red-900 text-sm font-medium"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
              <div class="flex-1 flex justify-between sm:hidden">
                <button
                  @click="previousPage"
                  :disabled="currentPage === 1"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  Previous
                </button>
                <button
                  @click="nextPage"
                  :disabled="currentPage === totalPages"
                  class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  Next
                </button>
              </div>
              <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p class="text-sm text-gray-700">
                    Showing {{ startIndex }} to {{ endIndex }} of {{ adminProductsStore.filteredProducts.length }} results
                  </p>
                </div>
                <div>
                  <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    <button
                      @click="previousPage"
                      :disabled="currentPage === 1"
                      class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      Previous
                    </button>
                    <button
                      @click="nextPage"
                      :disabled="currentPage === totalPages"
                      class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      Next
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, RouterLink } from 'vue-router'
import { useAdminProductsStore } from '@/stores/adminProducts'
import { useAdminNotificationsStore } from '@/stores/adminNotifications'
import AdminHeader from '@/components/admin/AdminHeader.vue'
import AdminSidebar from '@/components/admin/AdminSidebar.vue'

const router = useRouter()
const adminProductsStore = useAdminProductsStore()
const notificationsStore = useAdminNotificationsStore()

const selectedProducts = ref([])
const currentPage = ref(1)
const itemsPerPage = 12
const viewMode = ref('table')

const totalPages = computed(() =>
  Math.ceil(adminProductsStore.filteredProducts.length / itemsPerPage)
)

const startIndex = computed(() =>
  (currentPage.value - 1) * itemsPerPage + 1
)

const endIndex = computed(() =>
  Math.min(currentPage.value * itemsPerPage, adminProductsStore.filteredProducts.length)
)

const paginatedProducts = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return adminProductsStore.filteredProducts.slice(start, end)
})

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ar-DZ', {
    style: 'currency',
    currency: 'DZD',
    minimumFractionDigits: 0
  }).format(amount)
}

const toggleSelectAll = () => {
  if (selectedProducts.value.length === adminProductsStore.filteredProducts.length) {
    selectedProducts.value = []
  } else {
    selectedProducts.value = adminProductsStore.filteredProducts.map(p => p.id)
  }
}

const toggleProductStatus = async (product: any) => {
  const result = await adminProductsStore.toggleProductStatus(product.id)
  if (result.success) {
    notificationsStore.notifyProductUpdate(product, product.is_active ? 'deactivated' : 'activated')
  }
}

const editProduct = (product: any) => {
  router.push(`/admin/products/${product.id}/edit`)
}

const duplicateProduct = async (product: any) => {
  const duplicateData = {
    ...product,
    name: `${product.name} (Copy)`,
    id: undefined
  }

  const result = await adminProductsStore.createProduct(duplicateData)
  if (result.success) {
    notificationsStore.notifyProductUpdate(result.data, 'created')
  }
}

const deleteProduct = async (product: any) => {
  if (confirm(`Are you sure you want to delete "${product.name}"?`)) {
    const result = await adminProductsStore.deleteProduct(product.id)
    if (result.success) {
      notificationsStore.notifyProductUpdate(product, 'deleted')
      selectedProducts.value = selectedProducts.value.filter(id => id !== product.id)
    }
  }
}

const bulkAction = async (action: string) => {
  if (selectedProducts.value.length === 0) return

  const actionText = {
    activate: 'activate',
    deactivate: 'deactivate',
    delete: 'delete'
  }[action]

  if (confirm(`Are you sure you want to ${actionText} ${selectedProducts.value.length} products?`)) {
    const result = await adminProductsStore.bulkAction(action, selectedProducts.value)
    if (result.success) {
      notificationsStore.notifySystemAlert(
        'Bulk Action Completed',
        `Successfully ${actionText}d ${selectedProducts.value.length} products`,
        'success'
      )
      selectedProducts.value = []
    }
  }
}

const exportProducts = () => {
  // Export functionality
  notificationsStore.notifySystemAlert('Export Started', 'Products export is being prepared...', 'info')
}

const resetFilters = () => {
  adminProductsStore.resetFilters()
  currentPage.value = 1
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

// Watch for filter changes to reset pagination
watch(() => adminProductsStore.filters, () => {
  currentPage.value = 1
}, { deep: true })

onMounted(() => {
  adminProductsStore.fetchProducts()
})
</script>
