<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Ad<PERSON>er -->
    <AdminHeader />

    <!-- Main Content -->
    <div class="flex">
      <!-- Sidebar -->
      <AdminSidebar />

      <!-- Create Product Content -->
      <main class="flex-1 p-6">
        <!-- <PERSON> Header -->
        <div class="mb-8">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-gray-900">Create New Product</h1>
              <p class="text-gray-600 mt-2">Add a new product to your catalog</p>
            </div>
            <RouterLink to="/admin/products" class="btn-outline">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
              Back to Products
            </RouterLink>
          </div>
        </div>

        <!-- Create Product Form -->
        <div class="bg-white rounded-lg shadow">
          <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
            <!-- Basic Information -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Product Name -->
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Product Name *
                  </label>
                  <input
                    id="name"
                    v-model="form.name"
                    type="text"
                    required
                    class="input-field"
                    placeholder="e.g., Premium Cotton T-Shirt"
                  >
                </div>

                <!-- Category -->
                <div>
                  <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                    Category *
                  </label>
                  <select
                    id="category"
                    v-model="form.category_id"
                    required
                    class="input-field"
                  >
                    <option value="">Select a category</option>
                    <option value="1">T-Shirts</option>
                    <option value="2">Mugs</option>
                    <option value="3">Hoodies</option>
                    <option value="4">Accessories</option>
                  </select>
                </div>

                <!-- Base Price -->
                <div>
                  <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                    Base Price (DZD) *
                  </label>
                  <input
                    id="price"
                    v-model.number="form.base_price"
                    type="number"
                    min="0"
                    step="0.01"
                    required
                    class="input-field"
                    placeholder="1500.00"
                  >
                </div>

                <!-- Mockup Image URL -->
                <div>
                  <label for="mockup_image" class="block text-sm font-medium text-gray-700 mb-2">
                    Mockup Image URL
                  </label>
                  <input
                    id="mockup_image"
                    v-model="form.mockup_image"
                    type="url"
                    class="input-field"
                    placeholder="https://images.unsplash.com/..."
                  >
                </div>
              </div>

              <!-- Description -->
              <div class="mt-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  id="description"
                  v-model="form.description"
                  rows="4"
                  required
                  class="input-field"
                  placeholder="Describe your product features, materials, and benefits..."
                ></textarea>
              </div>
            </div>

            <!-- Available Sizes -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Available Sizes</h3>
              <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                <label v-for="size in availableSizes" :key="size" class="flex items-center">
                  <input
                    type="checkbox"
                    :value="size"
                    v-model="form.available_sizes"
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  >
                  <span class="ml-2 text-sm text-gray-700">{{ size }}</span>
                </label>
              </div>
            </div>

            <!-- Available Colors -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Available Colors</h3>
              <div class="space-y-3">
                <div v-for="(color, index) in form.available_colors" :key="index" class="flex items-center space-x-3">
                  <input
                    v-model="color.name"
                    type="text"
                    placeholder="Color name"
                    class="flex-1 input-field"
                  >
                  <input
                    v-model="color.hex"
                    type="color"
                    class="h-10 w-20 border border-gray-300 rounded"
                  >
                  <button
                    type="button"
                    @click="removeColor(index)"
                    class="text-red-600 hover:text-red-800"
                  >
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
                <button
                  type="button"
                  @click="addColor"
                  class="btn-outline text-sm"
                >
                  <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Add Color
                </button>
              </div>
            </div>

            <!-- Mockup Positions -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Design Positions</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Front Position -->
                <div class="border border-gray-200 rounded-lg p-4">
                  <h4 class="font-medium text-gray-900 mb-3">Front Position</h4>
                  <div class="grid grid-cols-2 gap-3">
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">X Position</label>
                      <input
                        v-model.number="form.mockup_positions.front.x"
                        type="number"
                        class="input-field text-sm"
                        placeholder="150"
                      >
                    </div>
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">Y Position</label>
                      <input
                        v-model.number="form.mockup_positions.front.y"
                        type="number"
                        class="input-field text-sm"
                        placeholder="100"
                      >
                    </div>
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">Width</label>
                      <input
                        v-model.number="form.mockup_positions.front.width"
                        type="number"
                        class="input-field text-sm"
                        placeholder="200"
                      >
                    </div>
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">Height</label>
                      <input
                        v-model.number="form.mockup_positions.front.height"
                        type="number"
                        class="input-field text-sm"
                        placeholder="250"
                      >
                    </div>
                  </div>
                </div>

                <!-- Back Position -->
                <div class="border border-gray-200 rounded-lg p-4">
                  <h4 class="font-medium text-gray-900 mb-3">Back Position</h4>
                  <div class="grid grid-cols-2 gap-3">
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">X Position</label>
                      <input
                        v-model.number="form.mockup_positions.back.x"
                        type="number"
                        class="input-field text-sm"
                        placeholder="150"
                      >
                    </div>
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">Y Position</label>
                      <input
                        v-model.number="form.mockup_positions.back.y"
                        type="number"
                        class="input-field text-sm"
                        placeholder="100"
                      >
                    </div>
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">Width</label>
                      <input
                        v-model.number="form.mockup_positions.back.width"
                        type="number"
                        class="input-field text-sm"
                        placeholder="200"
                      >
                    </div>
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">Height</label>
                      <input
                        v-model.number="form.mockup_positions.back.height"
                        type="number"
                        class="input-field text-sm"
                        placeholder="250"
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Error Message -->
            <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
              <div class="flex">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="ml-3">
                  <p class="text-sm text-red-800">{{ error }}</p>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              <RouterLink to="/admin/products" class="btn-outline">
                Cancel
              </RouterLink>
              <button
                type="submit"
                :disabled="isLoading"
                class="btn-primary"
              >
                <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ isLoading ? 'Creating...' : 'Create Product' }}
              </button>
            </div>
          </form>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter, RouterLink } from 'vue-router'
import { adminApi } from '@/services/adminApi'
import AdminHeader from '@/components/admin/AdminHeader.vue'
import AdminSidebar from '@/components/admin/AdminSidebar.vue'

const router = useRouter()

const isLoading = ref(false)
const error = ref('')

const availableSizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL', '11oz', '15oz', '10 inch']

const form = ref({
  name: '',
  description: '',
  base_price: 0,
  category_id: '',
  mockup_image: '',
  available_sizes: [],
  available_colors: [
    { name: 'White', hex: '#FFFFFF' },
    { name: 'Black', hex: '#000000' }
  ],
  mockup_positions: {
    front: { x: 150, y: 100, width: 200, height: 250 },
    back: { x: 150, y: 100, width: 200, height: 250 }
  }
})

const addColor = () => {
  form.value.available_colors.push({ name: '', hex: '#000000' })
}

const removeColor = (index: number) => {
  if (form.value.available_colors.length > 1) {
    form.value.available_colors.splice(index, 1)
  }
}

const handleSubmit = async () => {
  error.value = ''
  isLoading.value = true

  try {
    const result = await adminApi.createProduct(form.value)
    
    if (result.success) {
      // Redirect to products list
      router.push('/admin/products')
    } else {
      error.value = result.message || 'Failed to create product'
    }
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Network error occurred'
  } finally {
    isLoading.value = false
  }
}
</script>
