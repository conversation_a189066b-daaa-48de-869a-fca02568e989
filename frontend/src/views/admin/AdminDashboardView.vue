<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Ad<PERSON>er -->
    <AdminHeader />

    <!-- Main Content -->
    <div class="flex">
      <!-- Sidebar -->
      <AdminSidebar />

      <!-- Dashboard Content -->
      <main class="flex-1 p-6">
        <!-- <PERSON> Header -->
        <div class="mb-8">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
              <p class="text-gray-600 mt-2">Welcome back, {{ adminStore.admin?.name }}!</p>
            </div>
            <div class="flex items-center space-x-3">
              <button @click="refreshDashboard" class="btn-outline">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Refresh
              </button>
              <RouterLink to="/admin/products/create" class="btn-primary">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Product
              </RouterLink>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="adminStore.isLoading" class="flex items-center justify-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>

        <!-- Dashboard Content -->
        <div v-else-if="adminStore.dashboardStats" class="space-y-6">
          <!-- Stats Overview -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Total Products -->
            <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                  <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Total Products</p>
                  <p class="text-2xl font-bold text-gray-900">{{ adminStore.dashboardStats.overview.total_products }}</p>
                  <p class="text-xs text-green-600 mt-1">+2 this week</p>
                </div>
              </div>
            </div>

            <!-- Total Orders -->
            <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                  <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Total Orders</p>
                  <p class="text-2xl font-bold text-gray-900">{{ adminStore.dashboardStats.overview.total_orders }}</p>
                  <p class="text-xs text-green-600 mt-1">+12% from last month</p>
                </div>
              </div>
            </div>

            <!-- Total Users -->
            <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                  <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Total Users</p>
                  <p class="text-2xl font-bold text-gray-900">{{ adminStore.dashboardStats.overview.total_users }}</p>
                  <p class="text-xs text-green-600 mt-1">+89 this month</p>
                </div>
              </div>
            </div>

            <!-- Revenue Today -->
            <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100">
                  <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Revenue Today</p>
                  <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(adminStore.dashboardStats.overview.revenue_today) }}</p>
                  <p class="text-xs text-green-600 mt-1">+15% from yesterday</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Charts and Tables Row -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Sales Chart -->
            <AdminAnalyticsChart
              title="Sales Overview"
              :data="salesChartData"
              chart-type="sales"
              color="#10b981"
            />

            <!-- Top Products -->
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Top Products</h3>
                <RouterLink to="/admin/products" class="text-sm text-primary-600 hover:text-primary-800">
                  View all →
                </RouterLink>
              </div>
              <div class="space-y-4">
                <div
                  v-for="product in adminStore.dashboardStats.top_products"
                  :key="product.id"
                  class="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <img
                    :src="product.image"
                    :alt="product.name"
                    class="h-12 w-12 rounded-lg object-cover"
                  >
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">{{ product.name }}</p>
                    <p class="text-sm text-gray-500">{{ product.sales }} sales</p>
                  </div>
                  <div class="text-right">
                    <p class="text-sm font-medium text-gray-900">{{ formatCurrency(product.revenue) }}</p>
                    <p class="text-xs text-green-600">+{{ Math.round(Math.random() * 20) }}%</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Orders -->
          <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900">Recent Orders</h3>
              <RouterLink to="/admin/orders" class="text-sm text-primary-600 hover:text-primary-800">
                View all orders →
              </RouterLink>
            </div>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="order in adminStore.dashboardStats.recent_orders" :key="order.id" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ order.id }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ order.customer }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ order.product }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatCurrency(order.amount) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="getStatusClass(order.status)" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                        {{ order.status }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDate(order.created_at) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <RouterLink :to="`/admin/orders/${order.id}`" class="text-primary-600 hover:text-primary-900">
                        View
                      </RouterLink>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-lg font-semibold text-gray-900">Pending Orders</h4>
                  <p class="text-3xl font-bold text-yellow-600 mt-2">{{ adminStore.dashboardStats.overview.orders_pending }}</p>
                </div>
                <div class="p-3 rounded-full bg-yellow-100">
                  <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <RouterLink to="/admin/orders?status=pending" class="mt-4 btn-outline w-full text-center">
                Review Orders
              </RouterLink>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-lg font-semibold text-gray-900">Processing</h4>
                  <p class="text-3xl font-bold text-blue-600 mt-2">{{ adminStore.dashboardStats.overview.orders_processing }}</p>
                </div>
                <div class="p-3 rounded-full bg-blue-100">
                  <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
              </div>
              <RouterLink to="/admin/orders?status=processing" class="mt-4 btn-outline w-full text-center">
                Manage Orders
              </RouterLink>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-lg font-semibold text-gray-900">Monthly Revenue</h4>
                  <p class="text-3xl font-bold text-green-600 mt-2">{{ formatCurrency(adminStore.dashboardStats.overview.revenue_month) }}</p>
                </div>
                <div class="p-3 rounded-full bg-green-100">
                  <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <RouterLink to="/admin/analytics" class="mt-4 btn-outline w-full text-center">
                View Analytics
              </RouterLink>
            </div>
          </div>
        </div>

        <!-- Error State -->
        <div v-else-if="adminStore.error" class="text-center py-12">
          <svg class="h-12 w-12 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Error loading dashboard</h3>
          <p class="text-gray-500 mb-4">{{ adminStore.error }}</p>
          <button @click="loadDashboard" class="btn-primary">
            Try Again
          </button>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { RouterLink } from 'vue-router'
import { useAdminStore } from '@/stores/admin'
import AdminHeader from '@/components/admin/AdminHeader.vue'
import AdminSidebar from '@/components/admin/AdminSidebar.vue'
import AdminAnalyticsChart from '@/components/admin/AdminAnalyticsChart.vue'

const adminStore = useAdminStore()

const salesChartData = computed(() => {
  if (!adminStore.dashboardStats?.sales_chart) return []

  return adminStore.dashboardStats.sales_chart.map(item => ({
    date: item.date,
    value: item.sales,
    label: new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  }))
})

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ar-DZ', {
    style: 'currency',
    currency: 'DZD',
    minimumFractionDigits: 0
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getStatusClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const loadDashboard = async () => {
  await adminStore.fetchDashboardStats()
}

const refreshDashboard = async () => {
  await loadDashboard()
  // Show success notification
  // notificationsStore.notifySystemAlert('Dashboard Refreshed', 'Data has been updated successfully', 'success')
}

onMounted(() => {
  loadDashboard()
})
</script>
