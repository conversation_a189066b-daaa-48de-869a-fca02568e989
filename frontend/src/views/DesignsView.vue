<template>
  <div class="py-12">
    <div class="page-container">
      <div class="mb-8">
        <h1 class="section-title">Featured Designs</h1>
        <p class="text-xl text-gray-600">
          Discover amazing designs from our creative community.
        </p>
      </div>

      <!-- Filters -->
      <div class="mb-8 flex flex-wrap gap-4">
        <select 
          v-model="selectedProduct" 
          @change="handleProductChange"
          class="input-field w-auto"
        >
          <option value="">All Products</option>
          <option 
            v-for="product in productsStore.products" 
            :key="product.id" 
            :value="product.id"
          >
            {{ product.name }}
          </option>
        </select>

        <select 
          v-model="sortBy" 
          @change="handleSortChange"
          class="input-field w-auto"
        >
          <option value="created_at">Newest</option>
          <option value="views">Most Viewed</option>
          <option value="likes">Most Liked</option>
          <option value="name">Name</option>
        </select>

        <div class="relative flex-1 max-w-md">
          <input
            type="text"
            placeholder="Search designs..."
            v-model="searchQuery"
            @input="handleSearch"
            class="input-field pl-10"
          >
          <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
          </svg>
        </div>
      </div>

      <!-- Designs Grid -->
      <div v-if="!designsStore.isLoading" class="product-grid">
        <div 
          v-for="design in designsStore.designs" 
          :key="design.id"
          class="card hover:shadow-lg transition-shadow cursor-pointer"
          @click="$router.push(`/designs/${design.id}`)"
        >
          <div class="aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
            <svg class="h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 class="font-semibold text-lg mb-2">{{ design.name }}</h3>
          <p class="text-gray-600 text-sm mb-3">by {{ design.user?.name }}</p>
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4 text-sm text-gray-500">
              <span class="flex items-center">
                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                {{ design.views }}
              </span>
              <span class="flex items-center">
                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                {{ design.likes }}
              </span>
            </div>
            <button class="btn-primary text-sm" @click.stop="useDesign(design.id)">
              Use Design
            </button>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-else class="product-grid">
        <div v-for="i in 12" :key="i" class="card animate-pulse">
          <div class="aspect-square bg-gray-200 rounded-lg mb-4"></div>
          <div class="h-4 bg-gray-200 rounded mb-2"></div>
          <div class="h-3 bg-gray-200 rounded mb-3"></div>
          <div class="flex justify-between items-center">
            <div class="h-4 bg-gray-200 rounded w-20"></div>
            <div class="h-8 bg-gray-200 rounded w-20"></div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!designsStore.isLoading && designsStore.designs.length === 0" class="text-center py-12">
        <svg class="h-24 w-24 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">No designs found</h3>
        <p class="text-gray-500">Try adjusting your search or filter criteria.</p>
      </div>

      <!-- Pagination -->
      <div v-if="designsStore.pagination.last_page > 1" class="mt-12 flex justify-center">
        <nav class="flex items-center space-x-2">
          <button 
            @click="changePage(designsStore.pagination.current_page - 1)"
            :disabled="designsStore.pagination.current_page === 1"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <span class="px-3 py-2 text-sm text-gray-700">
            Page {{ designsStore.pagination.current_page }} of {{ designsStore.pagination.last_page }}
          </span>
          
          <button 
            @click="changePage(designsStore.pagination.current_page + 1)"
            :disabled="designsStore.pagination.current_page === designsStore.pagination.last_page"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useDesignsStore } from '@/stores/designs'
import { useProductsStore } from '@/stores/products'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const designsStore = useDesignsStore()
const productsStore = useProductsStore()
const authStore = useAuthStore()

const selectedProduct = ref('')
const sortBy = ref('created_at')
const searchQuery = ref('')

const fetchDesigns = async () => {
  const params: any = {
    page: 1,
    per_page: 12,
    sort_by: sortBy.value,
    sort_order: sortBy.value === 'created_at' ? 'desc' : 'desc'
  }

  if (selectedProduct.value) {
    params.product_id = selectedProduct.value
  }

  if (searchQuery.value) {
    params.search = searchQuery.value
  }

  await designsStore.fetchDesigns(params)
}

const handleProductChange = () => {
  fetchDesigns()
}

const handleSortChange = () => {
  fetchDesigns()
}

const handleSearch = () => {
  // Debounce search
  setTimeout(() => {
    fetchDesigns()
  }, 300)
}

const changePage = (page: number) => {
  if (page >= 1 && page <= designsStore.pagination.last_page) {
    const params: any = {
      page,
      per_page: 12,
      sort_by: sortBy.value,
      sort_order: sortBy.value === 'created_at' ? 'desc' : 'desc'
    }

    if (selectedProduct.value) {
      params.product_id = selectedProduct.value
    }

    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    designsStore.fetchDesigns(params)
  }
}

const useDesign = (designId: number) => {
  if (!authStore.isAuthenticated) {
    router.push({ name: 'login', query: { redirect: `/designs/${designId}` } })
    return
  }
  
  router.push(`/designs/${designId}`)
}

onMounted(async () => {
  // Load products and designs
  await Promise.all([
    productsStore.fetchProducts({ per_page: 100 }), // Get all products for filter
    fetchDesigns()
  ])
})
</script>
