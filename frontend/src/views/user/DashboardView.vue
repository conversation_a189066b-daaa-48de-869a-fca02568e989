<template>
  <div class="py-12">
    <div class="page-container">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {{ authStore.user?.name }}!
        </h1>
        <p class="text-gray-600">
          Manage your designs, orders, and account settings.
        </p>
      </div>

      <!-- Quick Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="card">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
              <svg class="h-6 w-6 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4a2 2 0 012-2z" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">My Designs</p>
              <p class="text-2xl font-bold text-gray-900">{{ designsStore.myDesigns.length }}</p>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center">
              <svg class="h-6 w-6 text-accent-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M8 11v6a2 2 0 002 2h4a2 2 0 002-2v-6M8 11h8" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Orders</p>
              <p class="text-2xl font-bold text-gray-900">0</p>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center">
              <svg class="h-6 w-6 text-secondary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Views</p>
              <p class="text-2xl font-bold text-gray-900">{{ totalViews }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div class="card">
          <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
          <div class="space-y-3">
            <RouterLink to="/products" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="h-5 w-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <div>
                <p class="font-medium">Create New Design</p>
                <p class="text-sm text-gray-600">Start designing on a product</p>
              </div>
            </RouterLink>

            <RouterLink to="/dashboard/designs" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
              <div class="w-10 h-10 bg-accent-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="h-5 w-5 text-accent-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div>
                <p class="font-medium">Manage Designs</p>
                <p class="text-sm text-gray-600">View and edit your designs</p>
              </div>
            </RouterLink>

            <RouterLink to="/dashboard/orders" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
              <div class="w-10 h-10 bg-secondary-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="h-5 w-5 text-secondary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M8 11v6a2 2 0 002 2h4a2 2 0 002-2v-6M8 11h8" />
                </svg>
              </div>
              <div>
                <p class="font-medium">View Orders</p>
                <p class="text-sm text-gray-600">Track your order history</p>
              </div>
            </RouterLink>
          </div>
        </div>

        <div class="card">
          <h3 class="text-lg font-semibold mb-4">Recent Designs</h3>
          <div class="space-y-3">
            <div v-if="recentDesigns.length === 0" class="text-center py-8 text-gray-500">
              <svg class="h-12 w-12 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4a2 2 0 012-2z" />
              </svg>
              <p>No designs yet</p>
              <RouterLink to="/products" class="text-primary-500 hover:text-primary-600 text-sm">
                Create your first design
              </RouterLink>
            </div>

            <div 
              v-for="design in recentDesigns" 
              :key="design.id"
              class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
              @click="$router.push(`/designs/${design.id}`)"
            >
              <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div class="flex-1">
                <p class="font-medium">{{ design.name }}</p>
                <p class="text-sm text-gray-600">{{ design.product?.name }}</p>
              </div>
              <div class="text-sm text-gray-500">
                {{ formatDate(design.created_at) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { RouterLink } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useDesignsStore } from '@/stores/designs'

const authStore = useAuthStore()
const designsStore = useDesignsStore()

const recentDesigns = computed(() => designsStore.myDesigns.slice(0, 5))
const totalViews = computed(() => 
  designsStore.myDesigns.reduce((total, design) => total + design.views, 0)
)

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  })
}

onMounted(async () => {
  // Fetch user's designs
  await designsStore.fetchMyDesigns({ per_page: 10 })
})
</script>
