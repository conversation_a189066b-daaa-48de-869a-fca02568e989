<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">Test Customization Link</h1>
      
      <!-- Test Product Links -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div v-for="productId in [1, 2, 3, 4, 5]" :key="productId" class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4">Product {{ productId }}</h3>
          <div class="space-y-3">
            <button 
              @click="testProductLoad(productId)"
              class="w-full btn-outline text-sm"
            >
              Test Product Load
            </button>
            <button 
              @click="testCustomizeLink(productId)"
              class="w-full btn-primary text-sm"
            >
              Test Customize Link
            </button>
            <RouterLink 
              :to="`/customize/${productId}`"
              class="block w-full text-center btn-secondary text-sm"
            >
              Direct Customize Link
            </RouterLink>
          </div>
        </div>
      </div>

      <!-- Test Results -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">Test Results</h2>
        <div v-if="testResults.length === 0" class="text-gray-500">
          No tests run yet. Click the buttons above to test.
        </div>
        <div v-else class="space-y-3">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            :class="[
              'p-3 rounded-lg border',
              result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
            ]"
          >
            <div class="flex items-center justify-between">
              <span class="font-medium">{{ result.test }}</span>
              <span :class="result.success ? 'text-green-600' : 'text-red-600'">
                {{ result.success ? '✓ Success' : '✗ Failed' }}
              </span>
            </div>
            <div class="text-sm text-gray-600 mt-1">{{ result.message }}</div>
            <div v-if="result.data" class="text-xs text-gray-500 mt-2">
              <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
        <button 
          @click="clearResults"
          class="mt-4 btn-outline text-sm"
        >
          Clear Results
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter, RouterLink } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const productsStore = useProductsStore()
const authStore = useAuthStore()

const testResults = ref([])

const addResult = (test: string, success: boolean, message: string, data?: any) => {
  testResults.value.unshift({
    test,
    success,
    message,
    data,
    timestamp: new Date().toLocaleTimeString()
  })
}

const testProductLoad = async (productId: number) => {
  try {
    addResult(`Loading Product ${productId}`, false, 'Testing...', null)
    
    const result = await productsStore.fetchProduct(productId.toString())
    
    if (result && result.success) {
      addResult(
        `Loading Product ${productId}`, 
        true, 
        `Product loaded successfully: ${result.data.name}`,
        result.data
      )
    } else {
      addResult(
        `Loading Product ${productId}`, 
        false, 
        `Failed to load product: ${result?.message || 'Unknown error'}`,
        result
      )
    }
  } catch (error) {
    addResult(
      `Loading Product ${productId}`, 
      false, 
      `Error loading product: ${error.message}`,
      error
    )
  }
}

const testCustomizeLink = async (productId: number) => {
  try {
    // First test if we can load the product
    const productResult = await productsStore.fetchProduct(productId.toString())
    
    if (!productResult || !productResult.success) {
      addResult(
        `Customize Link Product ${productId}`, 
        false, 
        'Cannot customize: Product not found',
        productResult
      )
      return
    }

    // Check authentication
    if (!authStore.isAuthenticated) {
      addResult(
        `Customize Link Product ${productId}`, 
        false, 
        'User not authenticated - would redirect to login',
        null
      )
      return
    }

    // Test the navigation
    addResult(
      `Customize Link Product ${productId}`, 
      true, 
      `Ready to navigate to /customize/${productId}`,
      { productName: productResult.data.name }
    )

    // Actually navigate
    router.push(`/customize/${productId}`)
    
  } catch (error) {
    addResult(
      `Customize Link Product ${productId}`, 
      false, 
      `Error testing customize link: ${error.message}`,
      error
    )
  }
}

const clearResults = () => {
  testResults.value = []
}
</script>
