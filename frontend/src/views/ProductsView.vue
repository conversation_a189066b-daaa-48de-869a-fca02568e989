<template>
  <div class="py-12">
    <div class="page-container">
      <div class="mb-8">
        <h1 class="section-title">Products</h1>
        <p class="text-xl text-gray-600">
          Choose from our wide range of high-quality products to customize.
        </p>
      </div>

      <!-- Filters -->
      <div class="mb-8 flex flex-wrap gap-4">
        <select 
          v-model="selectedCategory" 
          @change="handleCategoryChange"
          class="input-field w-auto"
        >
          <option value="">All Categories</option>
          <option 
            v-for="category in productsStore.activeCategories" 
            :key="category.id" 
            :value="category.id"
          >
            {{ category.name }}
          </option>
        </select>

        <select 
          v-model="sortBy" 
          @change="handleSortChange"
          class="input-field w-auto"
        >
          <option value="sort_order">Default</option>
          <option value="name">Name</option>
          <option value="base_price">Price</option>
          <option value="created_at">Newest</option>
        </select>

        <div class="relative flex-1 max-w-md">
          <input
            type="text"
            placeholder="Search products..."
            v-model="searchQuery"
            @input="handleSearch"
            class="input-field pl-10"
          >
          <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
          </svg>
        </div>
      </div>

      <!-- Products Grid -->
      <div v-if="!productsStore.isLoading && productsStore.products.length > 0" class="product-grid">
        <div
          v-for="product in productsStore.products"
          :key="product.id"
          class="group bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
        >
          <!-- Product Image -->
          <div class="relative aspect-square bg-gray-50 overflow-hidden">
            <img
              v-if="product.mockup_image"
              :src="product.mockup_image"
              :alt="product.name"
              class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              @error="handleImageError"
            >
            <div v-else class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
              <svg class="h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>

            <!-- Quick View Button -->
            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
              <button
                @click="$router.push(`/products/${product.id}`)"
                class="bg-white text-gray-900 px-4 py-2 rounded-lg font-medium opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-300 hover:bg-gray-50"
              >
                Quick View
              </button>
            </div>

            <!-- Category Badge -->
            <div class="absolute top-3 left-3">
              <span class="bg-white bg-opacity-90 text-gray-700 text-xs font-medium px-2 py-1 rounded-full">
                {{ product.category?.name || 'Product' }}
              </span>
            </div>

            <!-- Favorite Button -->
            <button class="absolute top-3 right-3 p-2 bg-white bg-opacity-90 rounded-full hover:bg-white transition-colors">
              <svg class="h-4 w-4 text-gray-600 hover:text-red-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </button>
          </div>

          <!-- Product Info -->
          <div class="p-6">
            <div class="mb-3">
              <h3 class="font-semibold text-lg text-gray-900 mb-1 line-clamp-1">{{ product.name }}</h3>
              <p class="text-gray-600 text-sm line-clamp-2">{{ product.description }}</p>
            </div>

            <!-- Product Features -->
            <div class="mb-4">
              <div class="flex items-center space-x-4 text-xs text-gray-500">
                <span v-if="product.available_sizes?.length" class="flex items-center">
                  <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4a2 2 0 012-2z" />
                  </svg>
                  {{ product.available_sizes.length }} sizes
                </span>
                <span v-if="product.available_colors?.length" class="flex items-center">
                  <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4a2 2 0 012-2z" />
                  </svg>
                  {{ product.available_colors.length }} colors
                </span>
              </div>
            </div>

            <!-- Price and Actions -->
            <div class="flex items-center justify-between">
              <div>
                <span class="text-2xl font-bold text-gray-900">
                  {{ formatPrice(product.base_price) }}
                </span>
                <span class="text-sm text-gray-500 ml-1">DZD</span>
              </div>

              <div class="flex items-center space-x-2">
                <button
                  @click="$router.push(`/products/${product.id}`)"
                  class="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  View
                </button>
                <button
                  @click.stop="customizeProduct(product.id)"
                  class="px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 transition-colors flex items-center space-x-1"
                >
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4a2 2 0 012-2z" />
                  </svg>
                  <span>Customize</span>
                </button>
                <button
                  @click="$router.push(`/debug-customize/${product.id}`)"
                  class="px-2 py-2 text-xs font-medium text-gray-600 bg-yellow-100 rounded-lg hover:bg-yellow-200 transition-colors"
                  title="Debug customize functionality"
                >
                  🐛
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-else class="product-grid">
        <div v-for="i in 12" :key="i" class="card animate-pulse">
          <div class="aspect-square bg-gray-200 rounded-lg mb-4"></div>
          <div class="h-4 bg-gray-200 rounded mb-2"></div>
          <div class="h-3 bg-gray-200 rounded mb-3"></div>
          <div class="flex justify-between items-center">
            <div class="h-4 bg-gray-200 rounded w-20"></div>
            <div class="h-8 bg-gray-200 rounded w-20"></div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!productsStore.isLoading && productsStore.products.length === 0" class="text-center py-12">
        <svg class="h-24 w-24 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
        </svg>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">No products found</h3>
        <p class="text-gray-500">Try adjusting your search or filter criteria.</p>
      </div>

      <!-- Pagination -->
      <div v-if="productsStore.pagination.last_page > 1" class="mt-12 flex justify-center">
        <nav class="flex items-center space-x-2">
          <button 
            @click="changePage(productsStore.pagination.current_page - 1)"
            :disabled="productsStore.pagination.current_page === 1"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <span class="px-3 py-2 text-sm text-gray-700">
            Page {{ productsStore.pagination.current_page }} of {{ productsStore.pagination.last_page }}
          </span>
          
          <button 
            @click="changePage(productsStore.pagination.current_page + 1)"
            :disabled="productsStore.pagination.current_page === productsStore.pagination.last_page"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const productsStore = useProductsStore()
const authStore = useAuthStore()

const selectedCategory = ref('')
const sortBy = ref('sort_order')
const searchQuery = ref('')

const formatPrice = (price: number) => {
  return new Intl.NumberFormat('fr-DZ', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price)
}

const fetchProducts = async () => {
  const params: any = {
    page: 1,
    per_page: 12,
    sort_by: sortBy.value,
    sort_order: 'asc'
  }

  if (selectedCategory.value) {
    params.category_id = selectedCategory.value
  }

  if (searchQuery.value) {
    params.search = searchQuery.value
  }

  await productsStore.fetchProducts(params)
}

const handleCategoryChange = () => {
  fetchProducts()
}

const handleSortChange = () => {
  fetchProducts()
}

const handleSearch = () => {
  // Debounce search
  setTimeout(() => {
    fetchProducts()
  }, 300)
}

const changePage = (page: number) => {
  if (page >= 1 && page <= productsStore.pagination.last_page) {
    const params: any = {
      page,
      per_page: 12,
      sort_by: sortBy.value,
      sort_order: 'asc'
    }

    if (selectedCategory.value) {
      params.category_id = selectedCategory.value
    }

    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    productsStore.fetchProducts(params)
  }
}

const customizeProduct = (productId: number) => {
  console.log('Customize button clicked for product:', productId)

  if (!authStore.isAuthenticated) {
    console.log('User not authenticated, redirecting to login')
    router.push({ name: 'login', query: { redirect: `/customize/${productId}` } })
    return
  }

  console.log('Navigating to customize page:', `/customize/${productId}`)
  router.push(`/customize/${productId}`)
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
  // Show fallback icon instead
}

onMounted(async () => {
  // Load categories and products
  await Promise.all([
    productsStore.fetchCategories(),
    fetchProducts()
  ])

  // Set initial values from query params
  if (route.query.category) {
    const category = productsStore.getCategoryBySlug(route.query.category as string)
    if (category) {
      selectedCategory.value = category.id.toString()
    }
  }

  if (route.query.search) {
    searchQuery.value = route.query.search as string
  }
})

// Watch for route changes
watch(() => route.query, (newQuery) => {
  if (newQuery.search !== searchQuery.value) {
    searchQuery.value = newQuery.search as string || ''
    fetchProducts()
  }
})
</script>
