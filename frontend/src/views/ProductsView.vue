<template>
  <div class="py-12">
    <div class="page-container">
      <div class="mb-8">
        <h1 class="section-title">Products</h1>
        <p class="text-xl text-gray-600">
          Choose from our wide range of high-quality products to customize.
        </p>
      </div>

      <!-- Filters -->
      <div class="mb-8 flex flex-wrap gap-4">
        <select 
          v-model="selectedCategory" 
          @change="handleCategoryChange"
          class="input-field w-auto"
        >
          <option value="">All Categories</option>
          <option 
            v-for="category in productsStore.activeCategories" 
            :key="category.id" 
            :value="category.id"
          >
            {{ category.name }}
          </option>
        </select>

        <select 
          v-model="sortBy" 
          @change="handleSortChange"
          class="input-field w-auto"
        >
          <option value="sort_order">Default</option>
          <option value="name">Name</option>
          <option value="base_price">Price</option>
          <option value="created_at">Newest</option>
        </select>

        <div class="relative flex-1 max-w-md">
          <input
            type="text"
            placeholder="Search products..."
            v-model="searchQuery"
            @input="handleSearch"
            class="input-field pl-10"
          >
          <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
          </svg>
        </div>
      </div>

      <!-- Products Grid -->
      <div v-if="!productsStore.isLoading" class="product-grid">
        <div 
          v-for="product in productsStore.products" 
          :key="product.id"
          class="card hover:shadow-lg transition-shadow cursor-pointer"
          @click="$router.push(`/products/${product.id}`)"
        >
          <div class="aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
            <svg class="h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
          <h3 class="font-semibold text-lg mb-2">{{ product.name }}</h3>
          <p class="text-gray-600 text-sm mb-3">{{ product.description }}</p>
          <div class="flex justify-between items-center">
            <span class="text-primary-500 font-semibold">
              {{ formatPrice(product.base_price) }} DZD
            </span>
            <button class="btn-primary text-sm" @click.stop="customizeProduct(product.id)">
              Customize
            </button>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-else class="product-grid">
        <div v-for="i in 12" :key="i" class="card animate-pulse">
          <div class="aspect-square bg-gray-200 rounded-lg mb-4"></div>
          <div class="h-4 bg-gray-200 rounded mb-2"></div>
          <div class="h-3 bg-gray-200 rounded mb-3"></div>
          <div class="flex justify-between items-center">
            <div class="h-4 bg-gray-200 rounded w-20"></div>
            <div class="h-8 bg-gray-200 rounded w-20"></div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!productsStore.isLoading && productsStore.products.length === 0" class="text-center py-12">
        <svg class="h-24 w-24 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
        </svg>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">No products found</h3>
        <p class="text-gray-500">Try adjusting your search or filter criteria.</p>
      </div>

      <!-- Pagination -->
      <div v-if="productsStore.pagination.last_page > 1" class="mt-12 flex justify-center">
        <nav class="flex items-center space-x-2">
          <button 
            @click="changePage(productsStore.pagination.current_page - 1)"
            :disabled="productsStore.pagination.current_page === 1"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <span class="px-3 py-2 text-sm text-gray-700">
            Page {{ productsStore.pagination.current_page }} of {{ productsStore.pagination.last_page }}
          </span>
          
          <button 
            @click="changePage(productsStore.pagination.current_page + 1)"
            :disabled="productsStore.pagination.current_page === productsStore.pagination.last_page"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const productsStore = useProductsStore()
const authStore = useAuthStore()

const selectedCategory = ref('')
const sortBy = ref('sort_order')
const searchQuery = ref('')

const formatPrice = (price: number) => {
  return new Intl.NumberFormat('fr-DZ', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price)
}

const fetchProducts = async () => {
  const params: any = {
    page: 1,
    per_page: 12,
    sort_by: sortBy.value,
    sort_order: 'asc'
  }

  if (selectedCategory.value) {
    params.category_id = selectedCategory.value
  }

  if (searchQuery.value) {
    params.search = searchQuery.value
  }

  await productsStore.fetchProducts(params)
}

const handleCategoryChange = () => {
  fetchProducts()
}

const handleSortChange = () => {
  fetchProducts()
}

const handleSearch = () => {
  // Debounce search
  setTimeout(() => {
    fetchProducts()
  }, 300)
}

const changePage = (page: number) => {
  if (page >= 1 && page <= productsStore.pagination.last_page) {
    const params: any = {
      page,
      per_page: 12,
      sort_by: sortBy.value,
      sort_order: 'asc'
    }

    if (selectedCategory.value) {
      params.category_id = selectedCategory.value
    }

    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    productsStore.fetchProducts(params)
  }
}

const customizeProduct = (productId: number) => {
  if (!authStore.isAuthenticated) {
    router.push({ name: 'login', query: { redirect: `/customize/${productId}` } })
    return
  }
  
  router.push(`/customize/${productId}`)
}

onMounted(async () => {
  // Load categories and products
  await Promise.all([
    productsStore.fetchCategories(),
    fetchProducts()
  ])

  // Set initial values from query params
  if (route.query.category) {
    const category = productsStore.getCategoryBySlug(route.query.category as string)
    if (category) {
      selectedCategory.value = category.id.toString()
    }
  }

  if (route.query.search) {
    searchQuery.value = route.query.search as string
  }
})

// Watch for route changes
watch(() => route.query, (newQuery) => {
  if (newQuery.search !== searchQuery.value) {
    searchQuery.value = newQuery.search as string || ''
    fetchProducts()
  }
})
</script>
