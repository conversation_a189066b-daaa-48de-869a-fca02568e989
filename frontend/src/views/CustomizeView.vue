<template>
  <div>
    <!-- Loading State -->
    <div v-if="isLoading" class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
        <p class="text-gray-600">Loading product...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <svg class="h-24 w-24 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">{{ error }}</h3>
        <RouterLink to="/products" class="btn-primary">
          Back to Products
        </RouterLink>
      </div>
    </div>

    <!-- Design Editor -->
    <PrintDesigner
      v-else-if="product"
      :product="product"
      :initial-design="initialDesign"
      @close="handleClose"
      @save="handleSave"
      @export="handleExport"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter, RouterLink } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import { useDesignsStore } from '@/stores/designs'
import { useAuthStore } from '@/stores/auth'
import PrintDesigner from '@/components/design/PrintDesigner.vue'

const route = useRoute()
const router = useRouter()
const productsStore = useProductsStore()
const designsStore = useDesignsStore()
const authStore = useAuthStore()

const product = ref(null)
const initialDesign = ref(null)
const isLoading = ref(true)
const error = ref('')

onMounted(async () => {
  try {
    const productId = route.params.productId as string
    const designId = route.query.design as string

    // Fetch product
    const productResult = await productsStore.fetchProduct(productId)
    if (!productResult.success) {
      error.value = 'Product not found'
      return
    }
    product.value = productResult.data

    // Fetch design if editing existing design
    if (designId) {
      const designResult = await designsStore.fetchDesign(designId)
      if (designResult.success) {
        initialDesign.value = designResult.data
      }
    }
  } catch (err) {
    error.value = 'Failed to load product'
  } finally {
    isLoading.value = false
  }
})

const handleClose = () => {
  router.back()
}

const handleSave = async (designData: any) => {
  try {
    // Create a blob from the design file data URL
    const response = await fetch(designData.preview)
    const blob = await response.blob()
    const file = new File([blob], 'design.png', { type: 'image/png' })

    const saveData = {
      name: `Design for ${product.value.name}`,
      product_id: product.value.id,
      design_data: designData.canvas,
      design_file: file,
      is_public: false
    }

    const result = await designsStore.createDesign(saveData)
    if (result.success) {
      // Show success message or redirect
      router.push('/dashboard/designs')
    } else {
      alert('Failed to save design: ' + result.message)
    }
  } catch (err) {
    alert('Failed to save design')
  }
}

const handleExport = async (designData: any) => {
  try {
    // First save the design
    await handleSave(designData)

    // Then redirect to order/checkout page
    // For now, just show a success message
    alert('Design exported successfully! Redirecting to checkout...')
    router.push('/dashboard/orders')
  } catch (err) {
    alert('Failed to export design')
  }
}
</script>
