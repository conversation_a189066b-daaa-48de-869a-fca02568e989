<template>
  <div class="py-12">
    <div class="page-container">
      <div v-if="productsStore.currentProduct" class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Product Image -->
        <div>
          <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
            <img
              v-if="productsStore.currentProduct.mockup_image"
              :src="productsStore.currentProduct.mockup_image"
              :alt="productsStore.currentProduct.name"
              class="w-full h-full object-cover"
            >
            <div v-else class="w-full h-full flex items-center justify-center">
              <svg class="h-32 w-32 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Product Details -->
        <div>
          <nav class="text-sm text-gray-500 mb-4">
            <RouterLink to="/products" class="hover:text-primary-500">Products</RouterLink>
            <span class="mx-2">/</span>
            <span>{{ productsStore.currentProduct.name }}</span>
          </nav>

          <h1 class="text-3xl font-bold text-gray-900 mb-4">
            {{ productsStore.currentProduct.name }}
          </h1>

          <p class="text-xl text-gray-600 mb-6">
            {{ productsStore.currentProduct.description }}
          </p>

          <div class="mb-6">
            <span class="text-3xl font-bold text-primary-500">
              {{ formatPrice(productsStore.currentProduct.base_price) }} DZD
            </span>
            <span class="text-gray-500 ml-2">starting price</span>
          </div>

          <!-- Available Sizes -->
          <div v-if="productsStore.currentProduct.available_sizes?.length" class="mb-6">
            <h3 class="text-lg font-semibold mb-3">Available Sizes</h3>
            <div class="flex flex-wrap gap-2">
              <span 
                v-for="size in productsStore.currentProduct.available_sizes" 
                :key="size"
                class="px-3 py-1 border border-gray-300 rounded text-sm"
              >
                {{ size }}
              </span>
            </div>
          </div>

          <!-- Available Colors -->
          <div v-if="productsStore.currentProduct.available_colors?.length" class="mb-6">
            <h3 class="text-lg font-semibold mb-3">Available Colors</h3>
            <div class="flex flex-wrap gap-2">
              <div 
                v-for="color in productsStore.currentProduct.available_colors" 
                :key="color.name"
                class="flex items-center space-x-2"
              >
                <div 
                  class="w-6 h-6 rounded-full border border-gray-300"
                  :style="{ backgroundColor: color.hex }"
                ></div>
                <span class="text-sm">{{ color.name }}</span>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="space-y-4">
            <button 
              @click="customizeProduct"
              class="btn-primary w-full text-lg py-3"
            >
              Customize This Product
            </button>
            
            <RouterLink 
              to="/products" 
              class="btn-outline w-full text-center block py-3"
            >
              Browse Other Products
            </RouterLink>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-else-if="productsStore.isLoading" class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <div class="aspect-square bg-gray-200 rounded-lg animate-pulse"></div>
        <div class="space-y-4">
          <div class="h-8 bg-gray-200 rounded animate-pulse"></div>
          <div class="h-6 bg-gray-200 rounded animate-pulse"></div>
          <div class="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
          <div class="h-12 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else class="text-center py-12">
        <svg class="h-24 w-24 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">Product not found</h3>
        <p class="text-gray-500 mb-4">The product you're looking for doesn't exist.</p>
        <RouterLink to="/products" class="btn-primary">
          Browse Products
        </RouterLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRoute, useRouter, RouterLink } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const productsStore = useProductsStore()
const authStore = useAuthStore()

const formatPrice = (price: number) => {
  return new Intl.NumberFormat('fr-DZ', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price)
}

const customizeProduct = () => {
  // For demo purposes, allow customization without authentication
  // In production, you can uncomment the auth check below

  // if (!authStore.isAuthenticated) {
  //   router.push({ name: 'login', query: { redirect: route.fullPath } })
  //   return
  // }

  router.push(`/customize/${route.params.id}`)
}

onMounted(async () => {
  const productId = route.params.id as string
  await productsStore.fetchProduct(productId)
})
</script>
