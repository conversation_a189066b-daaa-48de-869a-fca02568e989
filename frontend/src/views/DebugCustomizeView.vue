<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">Debug Customize View</h1>
      
      <!-- Debug Info -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Debug Information</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong>Product ID from Route:</strong> {{ route.params.productId }}
          </div>
          <div>
            <strong>Is Loading:</strong> {{ isLoading }}
          </div>
          <div>
            <strong>Has Error:</strong> {{ !!error }}
          </div>
          <div>
            <strong>Product Loaded:</strong> {{ !!product }}
          </div>
          <div>
            <strong>Auth Status:</strong> {{ authStore.isAuthenticated ? 'Authenticated' : 'Not Authenticated' }}
          </div>
          <div>
            <strong>API Base URL:</strong> {{ apiBaseUrl }}
          </div>
        </div>
      </div>

      <!-- Error Display -->
      <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-red-800 mb-2">Error</h3>
        <p class="text-red-700">{{ error }}</p>
        <div v-if="errorDetails" class="mt-4">
          <details>
            <summary class="cursor-pointer text-red-600">Error Details</summary>
            <pre class="mt-2 text-xs bg-red-100 p-2 rounded overflow-auto">{{ JSON.stringify(errorDetails, null, 2) }}</pre>
          </details>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <div class="flex items-center">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-3"></div>
          <span class="text-blue-700">Loading product...</span>
        </div>
      </div>

      <!-- Product Data -->
      <div v-if="product" class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-green-800 mb-4">Product Loaded Successfully</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="font-medium text-green-700 mb-2">Product Info</h4>
            <div class="text-sm space-y-1">
              <div><strong>ID:</strong> {{ product.id }}</div>
              <div><strong>Name:</strong> {{ product.name }}</div>
              <div><strong>Price:</strong> {{ formatPrice(product.base_price) }} DZD</div>
              <div><strong>Category:</strong> {{ product.category?.name || 'N/A' }}</div>
              <div><strong>Active:</strong> {{ product.is_active ? 'Yes' : 'No' }}</div>
            </div>
          </div>
          <div>
            <h4 class="font-medium text-green-700 mb-2">Product Image</h4>
            <img 
              v-if="product.mockup_image"
              :src="product.mockup_image" 
              :alt="product.name"
              class="w-32 h-32 object-cover rounded-lg border"
            >
            <div v-else class="w-32 h-32 bg-gray-200 rounded-lg flex items-center justify-center">
              <span class="text-gray-500 text-xs">No Image</span>
            </div>
          </div>
        </div>
        
        <!-- Raw Product Data -->
        <details class="mt-4">
          <summary class="cursor-pointer text-green-600">Raw Product Data</summary>
          <pre class="mt-2 text-xs bg-green-100 p-2 rounded overflow-auto">{{ JSON.stringify(product, null, 2) }}</pre>
        </details>
      </div>

      <!-- Test Actions -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Test Actions</h3>
        <div class="flex flex-wrap gap-3">
          <button 
            @click="reloadProduct"
            class="btn-primary"
            :disabled="isLoading"
          >
            Reload Product
          </button>
          <button 
            @click="testApiConnection"
            class="btn-outline"
            :disabled="isLoading"
          >
            Test API Connection
          </button>
          <button 
            @click="clearError"
            class="btn-outline"
            v-if="error"
          >
            Clear Error
          </button>
          <RouterLink 
            to="/products"
            class="btn-outline"
          >
            Back to Products
          </RouterLink>
          <RouterLink 
            :to="`/customize/${route.params.productId}`"
            class="btn-secondary"
            v-if="product"
          >
            Go to Real Customize Page
          </RouterLink>
        </div>
      </div>

      <!-- API Test Results -->
      <div v-if="apiTestResults.length > 0" class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">API Test Results</h3>
        <div class="space-y-3">
          <div 
            v-for="(result, index) in apiTestResults" 
            :key="index"
            :class="[
              'p-3 rounded-lg border text-sm',
              result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
            ]"
          >
            <div class="flex items-center justify-between">
              <span class="font-medium">{{ result.test }}</span>
              <span :class="result.success ? 'text-green-600' : 'text-red-600'">
                {{ result.success ? '✓ Success' : '✗ Failed' }}
              </span>
            </div>
            <div class="text-gray-600 mt-1">{{ result.message }}</div>
            <div class="text-xs text-gray-500 mt-1">{{ result.timestamp }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, RouterLink } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import { useAuthStore } from '@/stores/auth'
import { productsAPI } from '@/services/api'

const route = useRoute()
const productsStore = useProductsStore()
const authStore = useAuthStore()

const isLoading = ref(false)
const error = ref('')
const errorDetails = ref(null)
const product = ref(null)
const apiTestResults = ref([])

const apiBaseUrl = computed(() => {
  return import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1'
})

const formatPrice = (price: number) => {
  return new Intl.NumberFormat('fr-DZ', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price)
}

const addTestResult = (test: string, success: boolean, message: string) => {
  apiTestResults.value.unshift({
    test,
    success,
    message,
    timestamp: new Date().toLocaleTimeString()
  })
}

const loadProduct = async () => {
  try {
    isLoading.value = true
    error.value = ''
    errorDetails.value = null
    
    const productId = route.params.productId as string
    console.log('Loading product with ID:', productId)
    
    // Test direct API call first
    try {
      const apiResponse = await productsAPI.getById(productId)
      console.log('Direct API response:', apiResponse)
      addTestResult('Direct API Call', true, `API returned: ${apiResponse.data?.data?.name || 'Product data'}`)
    } catch (apiError) {
      console.error('Direct API error:', apiError)
      addTestResult('Direct API Call', false, `API Error: ${apiError.message}`)
    }
    
    // Test through store
    const result = await productsStore.fetchProduct(productId)
    console.log('Store result:', result)
    
    if (result && result.success) {
      product.value = result.data
      addTestResult('Store Product Load', true, `Loaded: ${result.data.name}`)
    } else {
      error.value = result?.message || 'Failed to load product'
      errorDetails.value = result
      addTestResult('Store Product Load', false, error.value)
    }
  } catch (err: any) {
    console.error('Load product error:', err)
    error.value = `Error loading product: ${err.message}`
    errorDetails.value = err
    addTestResult('Product Load', false, error.value)
  } finally {
    isLoading.value = false
  }
}

const reloadProduct = () => {
  product.value = null
  loadProduct()
}

const testApiConnection = async () => {
  try {
    addTestResult('API Connection Test', false, 'Testing...')
    
    // Test base API
    const response = await fetch(`${apiBaseUrl.value}/products`)
    const data = await response.json()
    
    if (response.ok && data.success) {
      addTestResult('API Connection Test', true, `Connected! Found ${data.data?.data?.length || 0} products`)
    } else {
      addTestResult('API Connection Test', false, `API Error: ${data.message || 'Unknown error'}`)
    }
  } catch (err: any) {
    addTestResult('API Connection Test', false, `Connection Error: ${err.message}`)
  }
}

const clearError = () => {
  error.value = ''
  errorDetails.value = null
}

onMounted(() => {
  loadProduct()
})
</script>
