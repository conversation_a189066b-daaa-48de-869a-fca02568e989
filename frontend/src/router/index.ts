import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAdminStore } from '@/stores/admin'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
    },
    {
      path: '/products',
      name: 'products',
      component: () => import('../views/ProductsView.vue'),
    },
    {
      path: '/products/:id',
      name: 'product-detail',
      component: () => import('../views/ProductDetailView.vue'),
      props: true,
    },
    {
      path: '/customize/:productId',
      name: 'customize',
      component: () => import('../views/CustomizeView.vue'),
      props: true,
      // meta: { requiresAuth: true }, // Disabled for demo
    },
    {
      path: '/test-customize',
      name: 'test-customize',
      component: () => import('../views/TestCustomizeView.vue'),
    },
    {
      path: '/debug-customize/:productId',
      name: 'debug-customize',
      component: () => import('../views/DebugCustomizeView.vue'),
    },
    {
      path: '/designs',
      name: 'designs',
      component: () => import('../views/DesignsView.vue'),
    },
    {
      path: '/designs/:id',
      name: 'design-detail',
      component: () => import('../views/DesignDetailView.vue'),
      props: true,
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/auth/LoginView.vue'),
      meta: { guest: true },
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/auth/RegisterView.vue'),
      meta: { guest: true },
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/user/DashboardView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/dashboard/designs',
      name: 'my-designs',
      component: () => import('../views/user/MyDesignsView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/dashboard/orders',
      name: 'my-orders',
      component: () => import('../views/user/MyOrdersView.vue'),
      meta: { requiresAuth: true },
    },
    // Admin routes
    {
      path: '/admin/login',
      name: 'admin-login',
      component: () => import('../views/admin/AdminLoginView.vue'),
    },
    {
      path: '/admin/dashboard',
      name: 'admin-dashboard',
      component: () => import('../views/admin/AdminDashboardView.vue'),
      meta: { requiresAdminAuth: true },
    },
    {
      path: '/admin/products',
      name: 'admin-products',
      component: () => import('../views/admin/AdminProductsView.vue'),
      meta: { requiresAdminAuth: true },
    },
    {
      path: '/admin/products/create',
      name: 'admin-products-create',
      component: () => import('../views/admin/AdminProductCreateView.vue'),
      meta: { requiresAdminAuth: true },
    },
    {
      path: '/admin/orders',
      name: 'admin-orders',
      component: () => import('../views/admin/AdminOrdersView.vue'),
      meta: { requiresAdminAuth: true },
    },
    {
      path: '/admin/users',
      name: 'admin-users',
      component: () => import('../views/admin/AdminUsersView.vue'),
      meta: { requiresAdminAuth: true },
    },
    {
      path: '/admin',
      redirect: '/admin/dashboard'
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/contact',
      name: 'contact',
      component: () => import('../views/ContactView.vue'),
    },
  ],
})

// Navigation guards
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  const adminStore = useAdminStore()

  // Check if route requires admin authentication
  if (to.meta.requiresAdminAuth && !adminStore.isAuthenticated) {
    next({ name: 'admin-login', query: { redirect: to.fullPath } })
    return
  }

  // Check if route requires user authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next({ name: 'login', query: { redirect: to.fullPath } })
    return
  }

  // Check if route requires admin privileges (legacy)
  if (to.meta.requiresAdmin && !authStore.isAdmin) {
    next({ name: 'home' })
    return
  }

  // Redirect authenticated users away from guest-only pages
  if (to.meta.guest && authStore.isAuthenticated) {
    next({ name: 'dashboard' })
    return
  }

  next()
})

export default router
