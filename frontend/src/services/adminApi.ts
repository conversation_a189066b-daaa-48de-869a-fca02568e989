import axios from 'axios'

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1'

// Create axios instance for admin API
const adminApiClient = axios.create({
  baseURL: `${API_URL}/admin`,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// Add token to requests
adminApiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('admin_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Handle response errors
adminApiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('admin_token')
      window.location.href = '/admin/login'
    }
    return Promise.reject(error)
  }
)

export const adminApi = {
  // Authentication
  async login(email: string, password: string) {
    const response = await adminApiClient.post('/login', { email, password })
    return response.data
  },

  async logout() {
    const response = await adminApiClient.post('/logout')
    return response.data
  },

  async getProfile() {
    const response = await adminApiClient.get('/profile')
    return response.data
  },

  // Dashboard
  async getDashboardStats() {
    const response = await adminApiClient.get('/dashboard/stats')
    return response.data
  },

  // Products
  async getProducts(params?: any) {
    const response = await adminApiClient.get('/products', { params })
    return response.data
  },

  async getProduct(id: number) {
    const response = await adminApiClient.get(`/products/${id}`)
    return response.data
  },

  async createProduct(productData: any) {
    const response = await adminApiClient.post('/products', productData)
    return response.data
  },

  async updateProduct(id: number, productData: any) {
    const response = await adminApiClient.put(`/products/${id}`, productData)
    return response.data
  },

  async deleteProduct(id: number) {
    const response = await adminApiClient.delete(`/products/${id}`)
    return response.data
  },

  async toggleProductStatus(id: number) {
    const response = await adminApiClient.post(`/products/${id}/toggle-status`)
    return response.data
  },

  async bulkProductAction(action: string, productIds: number[]) {
    const response = await adminApiClient.post('/products/bulk-action', {
      action,
      product_ids: productIds
    })
    return response.data
  },

  // Orders
  async getOrders(params?: any) {
    const response = await adminApiClient.get('/orders', { params })
    return response.data
  },

  async getOrder(id: number) {
    const response = await adminApiClient.get(`/orders/${id}`)
    return response.data
  },

  async updateOrderStatus(id: number, status: string) {
    const response = await adminApiClient.put(`/orders/${id}/status`, { status })
    return response.data
  },

  // Users
  async getUsers(params?: any) {
    const response = await adminApiClient.get('/users', { params })
    return response.data
  },

  async getUser(id: number) {
    const response = await adminApiClient.get(`/users/${id}`)
    return response.data
  },

  async updateUser(id: number, userData: any) {
    const response = await adminApiClient.put(`/users/${id}`, userData)
    return response.data
  },

  async deleteUser(id: number) {
    const response = await adminApiClient.delete(`/users/${id}`)
    return response.data
  },

  // Designs
  async getDesigns(params?: any) {
    const response = await adminApiClient.get('/designs', { params })
    return response.data
  },

  async moderateDesign(id: number, action: 'approve' | 'reject', reason?: string) {
    const response = await adminApiClient.post(`/designs/${id}/moderate`, {
      action,
      reason
    })
    return response.data
  },

  // Analytics
  async getAnalytics(period: string = '7d') {
    const response = await adminApiClient.get('/analytics', {
      params: { period }
    })
    return response.data
  },

  async getSalesReport(startDate: string, endDate: string) {
    const response = await adminApiClient.get('/analytics/sales', {
      params: { start_date: startDate, end_date: endDate }
    })
    return response.data
  },

  // Settings
  async getSettings() {
    const response = await adminApiClient.get('/settings')
    return response.data
  },

  async updateSettings(settings: any) {
    const response = await adminApiClient.put('/settings', settings)
    return response.data
  }
}
