import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { admin<PERSON>pi } from '@/services/adminApi'

export interface Admin {
  id: number
  name: string
  email: string
  role: string
  permissions: string[]
  avatar: string
  last_login: string
  created_at: string
  updated_at: string
}

export interface DashboardStats {
  overview: {
    total_products: number
    total_orders: number
    total_users: number
    total_designs: number
    revenue_today: number
    revenue_month: number
    orders_pending: number
    orders_processing: number
  }
  recent_orders: Array<{
    id: string
    customer: string
    product: string
    amount: number
    status: string
    created_at: string
  }>
  top_products: Array<{
    id: number
    name: string
    sales: number
    revenue: number
    image: string
  }>
  sales_chart: Array<{
    date: string
    sales: number
  }>
}

export const useAdminStore = defineStore('admin', () => {
  // State
  const admin = ref<Admin | null>(null)
  const token = ref<string | null>(localStorage.getItem('admin_token'))
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const dashboardStats = ref<DashboardStats | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!admin.value && !!token.value)
  const hasPermission = computed(() => (permission: string) => {
    return admin.value?.permissions.includes(permission) || false
  })

  // Actions
  const login = async (email: string, password: string) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await adminApi.login(email, password)
      
      if (response.success) {
        admin.value = response.data.admin
        token.value = response.data.token
        
        // Store token in localStorage
        localStorage.setItem('admin_token', response.data.token)
        
        return { success: true }
      } else {
        error.value = response.message || 'Login failed'
        return { success: false, message: error.value }
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Network error occurred'
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    isLoading.value = true

    try {
      await adminApi.logout()
    } catch (err) {
      console.error('Logout error:', err)
    } finally {
      // Clear state regardless of API response
      admin.value = null
      token.value = null
      dashboardStats.value = null
      localStorage.removeItem('admin_token')
      isLoading.value = false
    }
  }

  const fetchProfile = async () => {
    if (!token.value) return { success: false, message: 'No token available' }

    isLoading.value = true
    error.value = null

    try {
      const response = await adminApi.getProfile()
      
      if (response.success) {
        admin.value = response.data
        return { success: true }
      } else {
        error.value = response.message || 'Failed to fetch profile'
        return { success: false, message: error.value }
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Network error occurred'
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const fetchDashboardStats = async () => {
    if (!token.value) return { success: false, message: 'No token available' }

    isLoading.value = true
    error.value = null

    try {
      const response = await adminApi.getDashboardStats()
      
      if (response.success) {
        dashboardStats.value = response.data
        return { success: true, data: response.data }
      } else {
        error.value = response.message || 'Failed to fetch dashboard stats'
        return { success: false, message: error.value }
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Network error occurred'
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  // Initialize admin if token exists
  const initializeAdmin = async () => {
    if (token.value) {
      await fetchProfile()
    }
  }

  return {
    // State
    admin,
    token,
    isLoading,
    error,
    dashboardStats,
    
    // Getters
    isAuthenticated,
    hasPermission,
    
    // Actions
    login,
    logout,
    fetchProfile,
    fetchDashboardStats,
    clearError,
    initializeAdmin
  }
})
