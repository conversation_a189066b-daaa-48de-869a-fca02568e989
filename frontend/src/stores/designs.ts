import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { designsAPI } from '@/services/api'
import type { Product } from './products'
import type { User } from './auth'

export interface Design {
  id: number
  name: string
  user_id: number
  product_id: number
  design_file: string
  preview_image: string
  design_data: any
  is_public: boolean
  is_featured: boolean
  views: number
  likes: number
  created_at: string
  updated_at: string
  user?: User
  product?: Product
}

export const useDesignsStore = defineStore('designs', () => {
  const designs = ref<Design[]>([])
  const myDesigns = ref<Design[]>([])
  const featuredDesigns = ref<Design[]>([])
  const currentDesign = ref<Design | null>(null)
  const isLoading = ref(false)
  const pagination = ref({
    current_page: 1,
    last_page: 1,
    per_page: 12,
    total: 0,
  })

  const publicDesigns = computed(() => 
    designs.value.filter(design => design.is_public)
  )

  async function fetchDesigns(params?: {
    product_id?: number
    user_id?: number
    search?: string
    sort_by?: string
    sort_order?: string
    page?: number
    per_page?: number
  }) {
    isLoading.value = true
    try {
      const response = await designsAPI.getAll(params)
      designs.value = response.data.data.data
      pagination.value = {
        current_page: response.data.data.current_page,
        last_page: response.data.data.last_page,
        per_page: response.data.data.per_page,
        total: response.data.data.total,
      }
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to fetch designs' 
      }
    } finally {
      isLoading.value = false
    }
  }

  async function fetchDesign(id: string) {
    isLoading.value = true
    try {
      const response = await designsAPI.getById(id)
      currentDesign.value = response.data.data
      return { success: true, data: response.data.data }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to fetch design' 
      }
    } finally {
      isLoading.value = false
    }
  }

  async function fetchFeaturedDesigns(limit = 10) {
    try {
      const response = await designsAPI.getFeatured({ limit })
      featuredDesigns.value = response.data.data
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to fetch featured designs' 
      }
    }
  }

  async function fetchMyDesigns(params?: {
    page?: number
    per_page?: number
  }) {
    isLoading.value = true
    try {
      const response = await designsAPI.getMy(params)
      myDesigns.value = response.data.data.data
      pagination.value = {
        current_page: response.data.data.current_page,
        last_page: response.data.data.last_page,
        per_page: response.data.data.per_page,
        total: response.data.data.total,
      }
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to fetch your designs' 
      }
    } finally {
      isLoading.value = false
    }
  }

  async function createDesign(designData: {
    name: string
    product_id: number
    design_data: any
    design_file: File
    is_public?: boolean
  }) {
    isLoading.value = true
    try {
      const formData = new FormData()
      formData.append('name', designData.name)
      formData.append('product_id', designData.product_id.toString())
      formData.append('design_data', JSON.stringify(designData.design_data))
      formData.append('design_file', designData.design_file)
      if (designData.is_public !== undefined) {
        formData.append('is_public', designData.is_public.toString())
      }

      const response = await designsAPI.create(formData)
      const newDesign = response.data.data
      
      myDesigns.value.unshift(newDesign)
      if (newDesign.is_public) {
        designs.value.unshift(newDesign)
      }
      
      return { success: true, data: newDesign }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to save design',
        errors: error.response?.data?.errors || {}
      }
    } finally {
      isLoading.value = false
    }
  }

  async function updateDesign(id: string, designData: {
    name?: string
    design_data?: any
    design_file?: File
    is_public?: boolean
  }) {
    isLoading.value = true
    try {
      const formData = new FormData()
      if (designData.name) formData.append('name', designData.name)
      if (designData.design_data) formData.append('design_data', JSON.stringify(designData.design_data))
      if (designData.design_file) formData.append('design_file', designData.design_file)
      if (designData.is_public !== undefined) formData.append('is_public', designData.is_public.toString())

      const response = await designsAPI.update(id, formData)
      const updatedDesign = response.data.data
      
      // Update in all relevant arrays
      const updateInArray = (array: Design[]) => {
        const index = array.findIndex(d => d.id === parseInt(id))
        if (index !== -1) {
          array[index] = updatedDesign
        }
      }
      
      updateInArray(designs.value)
      updateInArray(myDesigns.value)
      updateInArray(featuredDesigns.value)
      
      if (currentDesign.value?.id === parseInt(id)) {
        currentDesign.value = updatedDesign
      }
      
      return { success: true, data: updatedDesign }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to update design',
        errors: error.response?.data?.errors || {}
      }
    } finally {
      isLoading.value = false
    }
  }

  async function deleteDesign(id: string) {
    isLoading.value = true
    try {
      await designsAPI.delete(id)
      
      // Remove from all arrays
      const removeFromArray = (array: Design[]) => {
        const index = array.findIndex(d => d.id === parseInt(id))
        if (index !== -1) {
          array.splice(index, 1)
        }
      }
      
      removeFromArray(designs.value)
      removeFromArray(myDesigns.value)
      removeFromArray(featuredDesigns.value)
      
      if (currentDesign.value?.id === parseInt(id)) {
        currentDesign.value = null
      }
      
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to delete design' 
      }
    } finally {
      isLoading.value = false
    }
  }

  function getDesignsByProduct(productId: number) {
    return designs.value.filter(design => design.product_id === productId)
  }

  function getDesignsByUser(userId: number) {
    return designs.value.filter(design => design.user_id === userId)
  }

  return {
    designs,
    myDesigns,
    featuredDesigns,
    currentDesign,
    isLoading,
    pagination,
    publicDesigns,
    fetchDesigns,
    fetchDesign,
    fetchFeaturedDesigns,
    fetchMyDesigns,
    createDesign,
    updateDesign,
    deleteDesign,
    getDesignsByProduct,
    getDesignsByUser,
  }
})
