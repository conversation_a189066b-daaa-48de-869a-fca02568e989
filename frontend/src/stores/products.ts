import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { productsAPI, categoriesAPI } from '@/services/api'

export interface Category {
  id: number
  name: string
  slug: string
  description?: string
  image?: string
  is_active: boolean
  sort_order: number
}

export interface Product {
  id: number
  name: string
  slug: string
  description?: string
  base_price: number
  category_id: number
  category?: Category
  mockup_image: string
  mockup_positions: any
  available_sizes?: string[]
  available_colors?: Array<{ name: string; hex: string }>
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

export const useProductsStore = defineStore('products', () => {
  const products = ref<Product[]>([])
  const categories = ref<Category[]>([])
  const currentProduct = ref<Product | null>(null)
  const isLoading = ref(false)
  const pagination = ref({
    current_page: 1,
    last_page: 1,
    per_page: 12,
    total: 0,
  })

  const activeCategories = computed(() => 
    categories.value.filter(cat => cat.is_active)
  )

  async function fetchProducts(params?: {
    category_id?: number
    category?: string
    search?: string
    sort_by?: string
    sort_order?: string
    page?: number
    per_page?: number
  }) {
    isLoading.value = true
    try {
      const response = await productsAPI.getAll(params)
      products.value = response.data.data.data
      pagination.value = {
        current_page: response.data.data.current_page,
        last_page: response.data.data.last_page,
        per_page: response.data.data.per_page,
        total: response.data.data.total,
      }
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to fetch products' 
      }
    } finally {
      isLoading.value = false
    }
  }

  async function fetchProduct(id: string) {
    isLoading.value = true
    try {
      const response = await productsAPI.getById(id)
      currentProduct.value = response.data.data
      return { success: true, data: response.data.data }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to fetch product' 
      }
    } finally {
      isLoading.value = false
    }
  }

  async function fetchCategories() {
    try {
      const response = await categoriesAPI.getAll()
      categories.value = response.data.data
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to fetch categories' 
      }
    }
  }

  async function createProduct(productData: Partial<Product>) {
    isLoading.value = true
    try {
      const response = await productsAPI.create(productData)
      products.value.unshift(response.data.data)
      return { success: true, data: response.data.data }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to create product',
        errors: error.response?.data?.errors || {}
      }
    } finally {
      isLoading.value = false
    }
  }

  async function updateProduct(id: string, productData: Partial<Product>) {
    isLoading.value = true
    try {
      const response = await productsAPI.update(id, productData)
      const index = products.value.findIndex(p => p.id === parseInt(id))
      if (index !== -1) {
        products.value[index] = response.data.data
      }
      if (currentProduct.value?.id === parseInt(id)) {
        currentProduct.value = response.data.data
      }
      return { success: true, data: response.data.data }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to update product',
        errors: error.response?.data?.errors || {}
      }
    } finally {
      isLoading.value = false
    }
  }

  async function deleteProduct(id: string) {
    isLoading.value = true
    try {
      await productsAPI.delete(id)
      products.value = products.value.filter(p => p.id !== parseInt(id))
      if (currentProduct.value?.id === parseInt(id)) {
        currentProduct.value = null
      }
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to delete product' 
      }
    } finally {
      isLoading.value = false
    }
  }

  function getProductsByCategory(categoryId: number) {
    return products.value.filter(product => product.category_id === categoryId)
  }

  function getCategoryBySlug(slug: string) {
    return categories.value.find(category => category.slug === slug)
  }

  return {
    products,
    categories,
    currentProduct,
    isLoading,
    pagination,
    activeCategories,
    fetchProducts,
    fetchProduct,
    fetchCategories,
    createProduct,
    updateProduct,
    deleteProduct,
    getProductsByCategory,
    getCategoryBySlug,
  }
})
