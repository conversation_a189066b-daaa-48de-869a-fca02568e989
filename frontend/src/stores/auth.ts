import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { authAPI } from '@/services/api'

export interface User {
  id: number
  name: string
  email: string
  role: string
  avatar?: string
  phone?: string
  bio?: string
  address?: any
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const isLoading = ref(false)

  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  async function login(credentials: { email: string; password: string }) {
    isLoading.value = true
    try {
      const response = await authAPI.login(credentials)
      const { user: userData, token: authToken } = response.data.data

      user.value = userData
      token.value = authToken

      localStorage.setItem('auth_token', authToken)
      localStorage.setItem('user', JSON.stringify(userData))

      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Login failed'
      }
    } finally {
      isLoading.value = false
    }
  }

  async function register(userData: {
    name: string
    email: string
    password: string
    password_confirmation: string
    phone?: string
  }) {
    isLoading.value = true
    try {
      const response = await authAPI.register(userData)
      const { user: newUser, token: authToken } = response.data.data

      user.value = newUser
      token.value = authToken

      localStorage.setItem('auth_token', authToken)
      localStorage.setItem('user', JSON.stringify(newUser))

      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Registration failed',
        errors: error.response?.data?.errors || {}
      }
    } finally {
      isLoading.value = false
    }
  }

  async function logout() {
    try {
      await authAPI.logout()
    } catch (error) {
      // Continue with logout even if API call fails
    }

    user.value = null
    token.value = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user')
  }

  async function fetchUser() {
    if (!token.value) return

    try {
      const response = await authAPI.me()
      user.value = response.data.data
      localStorage.setItem('user', JSON.stringify(user.value))
    } catch (error) {
      // Token might be invalid, logout
      await logout()
    }
  }

  async function updateProfile(profileData: Partial<User>) {
    isLoading.value = true
    try {
      const response = await authAPI.updateProfile(profileData)
      user.value = response.data.data
      localStorage.setItem('user', JSON.stringify(user.value))
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Profile update failed',
        errors: error.response?.data?.errors || {}
      }
    } finally {
      isLoading.value = false
    }
  }

  // Initialize user from localStorage
  function initializeAuth() {
    const storedUser = localStorage.getItem('user')
    if (storedUser && token.value) {
      try {
        user.value = JSON.parse(storedUser)
      } catch (error) {
        // Invalid stored user data, clear it
        localStorage.removeItem('user')
        localStorage.removeItem('auth_token')
        token.value = null
      }
    }
  }

  return {
    user,
    token,
    isLoading,
    isAuthenticated,
    isAdmin,
    login,
    register,
    logout,
    fetchUser,
    updateProfile,
    initializeAuth,
  }
})
