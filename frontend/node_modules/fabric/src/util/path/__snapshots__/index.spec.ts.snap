// Jest <PERSON> v1, https://goo.gl/fbAQLP

exports[`Path Utils fabric.util.getRegularPolygonPath 1`] = `
[
  [
    "M",
    0,
    -50,
  ],
  [
    "L",
    47.552825814757675,
    -15.450849718747369,
  ],
  [
    "L",
    29.389262614623657,
    40.45084971874737,
  ],
  [
    "L",
    -29.38926261462365,
    40.45084971874737,
  ],
  [
    "L",
    -47.55282581475768,
    -15.450849718747364,
  ],
  [
    "Z",
  ],
]
`;

exports[`Path Utils fabric.util.getRegularPolygonPath 2`] = `
[
  [
    "M",
    24.999999999999993,
    -43.30127018922194,
  ],
  [
    "L",
    50,
    -1.1102230246251565e-14,
  ],
  [
    "L",
    25.000000000000018,
    43.301270189221924,
  ],
  [
    "L",
    -24.99999999999999,
    43.30127018922194,
  ],
  [
    "L",
    -50,
    2.8327694488239898e-14,
  ],
  [
    "L",
    -25.00000000000006,
    -43.301270189221896,
  ],
  [
    "Z",
  ],
]
`;

exports[`Path Utils getPathSegmentsInfo operates as expected 1`] = `
[
  {
    "command": "M",
    "length": 0,
    "x": 50,
    "y": 50,
  },
  {
    "angleFinder": [Function],
    "command": "Q",
    "iterator": [Function],
    "length": 35.35533905932738,
    "x": 50,
    "y": 50,
  },
  {
    "angleFinder": [Function],
    "command": "Q",
    "iterator": [Function],
    "length": 80.4908603478418,
    "x": 75,
    "y": 75,
  },
  {
    "angleFinder": [Function],
    "command": "Q",
    "iterator": [Function],
    "length": 174.94631067949948,
    "x": 75,
    "y": 150,
  },
  {
    "angleFinder": [Function],
    "command": "Q",
    "iterator": [Function],
    "length": 301.55412388505005,
    "x": 225,
    "y": 175,
  },
  {
    "command": "L",
    "length": 182.00274723201295,
    "x": 450,
    "y": 325,
  },
  {
    "length": 774.3493812037316,
    "x": 500,
    "y": 500,
  },
]
`;

exports[`Path Utils makePathSimpler can parse paths that return NaN segments 1`] = `
[
  [
    "M",
    22,
    6.58,
  ],
  [
    "C",
    21.86757184091244,
    6.561276219544052,
    21.739164165358723,
    6.520726427263932,
    21.62,
    6.46,
  ],
  [
    "C",
    21.62,
    6.26,
    21.740000000000002,
    6.26,
    21.71,
    5.96,
  ],
  [
    "L",
    21.71,
    5.9,
  ],
  [
    "C",
    21.71,
    5.99,
    21.71,
    6,
    21.650000000000002,
    5.9,
  ],
  [
    "C",
    21.590000000000003,
    5.800000000000001,
    21.55,
    5.61,
    21.580000000000002,
    5.46,
  ],
  [
    "C",
    21.610000000000003,
    5.31,
    21.76,
    5.65,
    21.720000000000002,
    5.28,
  ],
  [
    "C",
    21.650000000000002,
    5.28,
    21.51,
    5.08,
    21.51,
    5.28,
  ],
  [
    "L",
    21.560000000000002,
    5.28,
  ],
  [
    "L",
    21.560000000000002,
    5.73,
  ],
  [
    "C",
    21.560000000000002,
    5.57,
    21.490000000000002,
    5.840000000000001,
    21.44,
    5.58,
  ],
  [
    "C",
    21.44,
    5.53,
    21.5,
    5.53,
    21.52,
    5.49,
  ],
  [
    "C",
    21.54,
    5.45,
    21.41,
    5.49,
    21.37,
    5.42,
  ],
  [
    "L",
    21.43,
    5.51,
  ],
  [
    "C",
    21.41364349396296,
    5.60927934079435,
    21.355351504565153,
    5.69671732489106,
    21.27,
    5.75,
  ],
  [
    "L",
    21.21,
    5.75,
  ],
  [
    "C",
    21.21,
    5.65,
    21.1,
    5.64,
    21.11,
    5.46,
  ],
  [
    "C",
    21.119999999999997,
    5.28,
    21.18,
    5.53,
    21.16,
    5.46,
  ],
  [
    "C",
    21.16,
    5.22,
    21.1,
    5.46,
    21.05,
    5.25,
  ],
  [
    "C",
    21.05,
    5.4,
    21,
    5.49,
    21.05,
    5.66,
  ],
  [
    "C",
    21,
    5.5600000000000005,
    20.98,
    5.8100000000000005,
    20.91,
    5.66,
  ],
  [
    "C",
    20.91756710278662,
    5.699642118471354,
    20.91756710278662,
    5.740357881528646,
    20.91,
    5.78,
  ],
  [
    "C",
    20.91,
    5.78,
    20.86,
    5.78,
    20.84,
    5.84,
  ],
  [
    "L",
    20.84,
    5.84,
  ],
  [
    "C",
    20.84,
    5.84,
    20.84,
    5.84,
    20.84,
    5.84,
  ],
  [
    "C",
    20.84,
    5.84,
    20.84,
    5.63,
    20.9,
    5.49,
  ],
  [
    "L",
    20.9,
    5.49,
  ],
  [
    "C",
    20.9,
    5.49,
    20.959999999999997,
    5.49,
    20.9,
    5.49,
  ],
  [
    "L",
    20.9,
    5.390000000000001,
  ],
  [
    "C",
    20.9,
    5.120000000000001,
    20.97,
    4.8500000000000005,
    20.99,
    4.73,
  ],
  [
    "L",
    20.99,
    4.87,
  ],
  [
    "C",
    20.99,
    4.53,
    21.119999999999997,
    4.87,
    21.13,
    4.6,
  ],
  [
    "C",
    21.13,
    4.6,
    21.13,
    4.67,
    21.13,
    4.68,
  ],
  [
    "C",
    21.13,
    4.6899999999999995,
    21.13,
    4.239999999999999,
    21.27,
    4.449999999999999,
  ],
  [
    "L",
    21.27,
    4.609999999999999,
  ],
  [
    "C",
    21.284655737743563,
    4.503836748916477,
    21.284655737743563,
    4.396163251083522,
    21.27,
    4.289999999999999,
  ],
  [
    "C",
    21.27,
    4.349999999999999,
    21.27,
    4.419999999999999,
    21.169999999999998,
    4.389999999999999,
  ],
  [
    "C",
    21.16217733233167,
    4.313532879407623,
    21.16217733233167,
    4.2364671205923745,
    21.169999999999998,
    4.159999999999998,
  ],
  [
    "L",
    21.169999999999998,
    4.159999999999998,
  ],
  [
    "L",
    21.169999999999998,
    4.159999999999998,
  ],
  [
    "L",
    21.169999999999998,
    4.159999999999998,
  ],
  [
    "C",
    21.164440069923444,
    4.1403864102807,
    21.164440069923444,
    4.119613589719297,
    21.169999999999998,
    4.099999999999999,
  ],
]
`;

exports[`Path Utils parsePath Path written with uncommon scenario 1`] = `
[
  [
    "a",
    10.56,
    10.56,
    0,
    0,
    0,
    -1.484,
    -0.133,
  ],
  [
    "a",
    10.56,
    10.56,
    0,
    0,
    0,
    -1.484,
    -0.133,
  ],
  [
    "a",
    10.56,
    10.56,
    0,
    0,
    0,
    -1.484,
    -0.133,
  ],
  [
    "a",
    10.56,
    10.56,
    0,
    0,
    0,
    -1.484,
    -0.133,
  ],
]
`;

exports[`Path Utils parsePath can parse path string 1`] = `
[
  [
    "M",
    2,
    5,
  ],
  [
    "l",
    2,
    -2,
  ],
  [
    "L",
    4,
    4,
  ],
  [
    "h",
    3,
  ],
  [
    "H",
    9,
  ],
  [
    "C",
    8,
    3,
    10,
    3,
    10,
    3,
  ],
  [
    "c",
    1,
    -1,
    2,
    0,
    1,
    1,
  ],
  [
    "S",
    8,
    5,
    9,
    7,
  ],
  [
    "v",
    1,
  ],
  [
    "s",
    2,
    -1,
    1,
    2,
  ],
  [
    "Q",
    9,
    10,
    10,
    11,
  ],
  [
    "T",
    12,
    11,
  ],
  [
    "t",
    -1,
    -1,
  ],
  [
    "v",
    2,
  ],
  [
    "T",
    10,
    12,
  ],
  [
    "S",
    9,
    12,
    7,
    11,
  ],
  [
    "c",
    0,
    -1,
    0,
    -1,
    -2,
    -2,
  ],
  [
    "z",
  ],
  [
    "m",
    0,
    2,
  ],
  [
    "l",
    1,
    0,
  ],
  [
    "l",
    0,
    1,
  ],
  [
    "l",
    -1,
    0,
  ],
  [
    "z",
  ],
  [
    "M",
    1,
    1,
  ],
  [
    "a",
    1,
    1,
    30,
    1,
    0,
    2,
    2,
  ],
  [
    "A",
    2,
    2,
    30,
    1,
    0,
    6,
    6,
  ],
]
`;

exports[`Path Utils parsePath can parse path string 2`] = `
[
  [
    "M",
    2,
    5,
  ],
  [
    "L",
    4,
    3,
  ],
  [
    "L",
    4,
    4,
  ],
  [
    "L",
    7,
    4,
  ],
  [
    "L",
    9,
    4,
  ],
  [
    "C",
    8,
    3,
    10,
    3,
    10,
    3,
  ],
  [
    "C",
    11,
    2,
    12,
    3,
    11,
    4,
  ],
  [
    "C",
    10,
    5,
    8,
    5,
    9,
    7,
  ],
  [
    "L",
    9,
    8,
  ],
  [
    "C",
    9,
    8,
    11,
    7,
    10,
    10,
  ],
  [
    "Q",
    9,
    10,
    10,
    11,
  ],
  [
    "Q",
    11,
    12,
    12,
    11,
  ],
  [
    "Q",
    13,
    10,
    11,
    10,
  ],
  [
    "L",
    11,
    12,
  ],
  [
    "Q",
    11,
    12,
    10,
    12,
  ],
  [
    "C",
    10,
    12,
    9,
    12,
    7,
    11,
  ],
  [
    "C",
    7,
    10,
    7,
    10,
    5,
    9,
  ],
  [
    "Z",
  ],
  [
    "M",
    2,
    7,
  ],
  [
    "L",
    3,
    7,
  ],
  [
    "L",
    3,
    8,
  ],
  [
    "L",
    2,
    8,
  ],
  [
    "Z",
  ],
  [
    "M",
    1,
    1,
  ],
  [
    "C",
    0.44771525016920655,
    1.5522847498307935,
    0.44771525016920655,
    2.4477152501692068,
    1,
    3,
  ],
  [
    "C",
    1.5522847498307935,
    3.5522847498307932,
    2.4477152501692068,
    3.5522847498307932,
    3,
    3,
  ],
  [
    "C",
    2.1715728752538106,
    3.8284271247461903,
    2.1715728752538106,
    5.17157287525381,
    3.0000000000000004,
    6,
  ],
  [
    "C",
    3.8284271247461903,
    6.82842712474619,
    5.17157287525381,
    6.82842712474619,
    6,
    6,
  ],
]
`;
