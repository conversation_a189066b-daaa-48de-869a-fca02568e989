import{defineProperty as e}from"../_virtual/_rollupPluginBabelHelpers.min.mjs";class t{constructor(){e(this,"__eventListeners",{})}on(e,t){if(this.__eventListeners||(this.__eventListeners={}),"object"==typeof e)return Object.entries(e).forEach((e=>{let[t,s]=e;this.on(t,s)})),()=>this.off(e);if(t){const s=e;return this.__eventListeners[s]||(this.__eventListeners[s]=[]),this.__eventListeners[s].push(t),()=>this.off(s,t)}return()=>!1}once(e,t){if("object"==typeof e){const t=[];return Object.entries(e).forEach((e=>{let[s,n]=e;t.push(this.once(s,n))})),()=>t.forEach((e=>e()))}if(t){const s=this.on(e,(function(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];t.call(this,...n),s()}));return s}return()=>!1}_removeEventListener(e,t){if(this.__eventListeners[e])if(t){const s=this.__eventListeners[e],n=s.indexOf(t);n>-1&&s.splice(n,1)}else this.__eventListeners[e]=[]}off(e,t){if(this.__eventListeners)if(void 0===e)for(const e in this.__eventListeners)this._removeEventListener(e);else"object"==typeof e?Object.entries(e).forEach((e=>{let[t,s]=e;this._removeEventListener(t,s)})):this._removeEventListener(e,t)}fire(e,t){var s;if(!this.__eventListeners)return;const n=null===(s=this.__eventListeners[e])||void 0===s?void 0:s.concat();if(n)for(let e=0;e<n.length;e++)n[e].call(this,t||{})}}export{t as Observable};
//# sourceMappingURL=Observable.min.mjs.map
