import type { TClassProperties } from '../../typedefs';
import type { InteractiveFabricObject } from './InteractiveObject';
import type { FabricObject } from './Object';
export declare const stateProperties: string[];
export declare const cacheProperties: string[];
export declare const fabricObjectDefaultValues: Partial<TClassProperties<FabricObject>>;
export declare const interactiveObjectDefaultValues: Partial<TClassProperties<InteractiveFabricObject>>;
//# sourceMappingURL=defaultValues.d.ts.map