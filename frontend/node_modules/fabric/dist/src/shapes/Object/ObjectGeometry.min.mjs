import{objectSpread2 as t}from"../../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{SCALE_X as i,SCALE_Y as e,iMatrix as s,CENTER as r,LEFT as n,TOP as o}from"../../constants.min.mjs";import{Intersection as h}from"../../Intersection.min.mjs";import{Point as a}from"../../Point.min.mjs";import{makeBoundingBoxFromPoints as l}from"../../util/misc/boundingBoxFromPoints.min.mjs";import{transformPoint as c,invertTransform as g,calcPlaneRotation as m,createRotateMatrix as u,multiplyTransformMatrices as d,composeMatrix as f,calcDimensionsMatrix as p,createTranslateMatrix as v}from"../../util/misc/matrix.min.mjs";import{radiansToDegrees as C,degreesToRadians as x}from"../../util/misc/radiansDegreesConversion.min.mjs";import{resolveOrigin as y}from"../../util/misc/resolveOrigin.min.mjs";import{sizeAfterTransform as X}from"../../util/misc/objectTransforms.min.mjs";import{CommonMethods as Y}from"../../CommonMethods.min.mjs";class w extends Y{getX(){return this.getXY().x}setX(t){this.setXY(this.getXY().setX(t))}getY(){return this.getXY().y}setY(t){this.setXY(this.getXY().setY(t))}getRelativeX(){return this.left}setRelativeX(t){this.left=t}getRelativeY(){return this.top}setRelativeY(t){this.top=t}getXY(){const t=this.getRelativeXY();return this.group?c(t,this.group.calcTransformMatrix()):t}setXY(t,i,e){this.group&&(t=c(t,g(this.group.calcTransformMatrix()))),this.setRelativeXY(t,i,e)}getRelativeXY(){return new a(this.left,this.top)}setRelativeXY(t){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.originX,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.originY;this.setPositionByOrigin(t,i,e)}isStrokeAccountedForInDimensions(){return!1}getCoords(){const{tl:t,tr:i,br:e,bl:s}=this.aCoords||(this.aCoords=this.calcACoords()),r=[t,i,e,s];if(this.group){const t=this.group.calcTransformMatrix();return r.map((i=>c(i,t)))}return r}intersectsWithRect(t,i){return"Intersection"===h.intersectPolygonRectangle(this.getCoords(),t,i).status}intersectsWithObject(t){const i=h.intersectPolygonPolygon(this.getCoords(),t.getCoords());return"Intersection"===i.status||"Coincident"===i.status||t.isContainedWithinObject(this)||this.isContainedWithinObject(t)}isContainedWithinObject(t){return this.getCoords().every((i=>t.containsPoint(i)))}isContainedWithinRect(t,i){const{left:e,top:s,width:r,height:n}=this.getBoundingRect();return e>=t.x&&e+r<=i.x&&s>=t.y&&s+n<=i.y}isOverlapping(t){return this.intersectsWithObject(t)||this.isContainedWithinObject(t)||t.isContainedWithinObject(this)}containsPoint(t){return h.isPointInPolygon(t,this.getCoords())}isOnScreen(){if(!this.canvas)return!1;const{tl:t,br:i}=this.canvas.vptCoords;return!!this.getCoords().some((e=>e.x<=i.x&&e.x>=t.x&&e.y<=i.y&&e.y>=t.y))||(!!this.intersectsWithRect(t,i)||this.containsPoint(t.midPointFrom(i)))}isPartiallyOnScreen(){if(!this.canvas)return!1;const{tl:t,br:i}=this.canvas.vptCoords;if(this.intersectsWithRect(t,i))return!0;return this.getCoords().every((e=>(e.x>=i.x||e.x<=t.x)&&(e.y>=i.y||e.y<=t.y)))&&this.containsPoint(t.midPointFrom(i))}getBoundingRect(){return l(this.getCoords())}getScaledWidth(){return this._getTransformedDimensions().x}getScaledHeight(){return this._getTransformedDimensions().y}scale(t){this._set(i,t),this._set(e,t),this.setCoords()}scaleToWidth(t){const i=this.getBoundingRect().width/this.getScaledWidth();return this.scale(t/this.width/i)}scaleToHeight(t){const i=this.getBoundingRect().height/this.getScaledHeight();return this.scale(t/this.height/i)}getCanvasRetinaScaling(){var t;return(null===(t=this.canvas)||void 0===t?void 0:t.getRetinaScaling())||1}getTotalAngle(){return this.group?C(m(this.calcTransformMatrix())):this.angle}getViewportTransform(){var t;return(null===(t=this.canvas)||void 0===t?void 0:t.viewportTransform)||s.concat()}calcACoords(){const t=u({angle:this.angle}),{x:i,y:e}=this.getRelativeCenterPoint(),s=v(i,e),r=d(s,t),n=this._getTransformedDimensions(),o=n.x/2,h=n.y/2;return{tl:c({x:-o,y:-h},r),tr:c({x:o,y:-h},r),bl:c({x:-o,y:h},r),br:c({x:o,y:h},r)}}setCoords(){this.aCoords=this.calcACoords()}transformMatrixKey(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=[];return!t&&this.group&&(i=this.group.transformMatrixKey(t)),i.push(this.top,this.left,this.width,this.height,this.scaleX,this.scaleY,this.angle,this.strokeWidth,this.skewX,this.skewY,+this.flipX,+this.flipY,y(this.originX),y(this.originY)),i}calcTransformMatrix(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.calcOwnMatrix();if(t||!this.group)return i;const e=this.transformMatrixKey(t),s=this.matrixCache;return s&&s.key.every(((t,i)=>t===e[i]))?s.value:(this.group&&(i=d(this.group.calcTransformMatrix(!1),i)),this.matrixCache={key:e,value:i},i)}calcOwnMatrix(){const t=this.transformMatrixKey(!0),i=this.ownMatrixCache;if(i&&i.key===t)return i.value;const e=this.getRelativeCenterPoint(),s={angle:this.angle,translateX:e.x,translateY:e.y,scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,flipX:this.flipX,flipY:this.flipY},r=f(s);return this.ownMatrixCache={key:t,value:r},r}_getNonTransformedDimensions(){return new a(this.width,this.height).scalarAdd(this.strokeWidth)}_calculateCurrentDimensions(t){return this._getTransformedDimensions(t).transform(this.getViewportTransform(),!0).scalarAdd(2*this.padding)}_getTransformedDimensions(){let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const e=t({scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,width:this.width,height:this.height,strokeWidth:this.strokeWidth},i),s=e.strokeWidth;let r=s,n=0;this.strokeUniform&&(r=0,n=s);const o=e.width+r,h=e.height+r;let l;return l=0===e.skewX&&0===e.skewY?new a(o*e.scaleX,h*e.scaleY):X(o,h,p(e)),l.scalarAdd(n)}translateToGivenOrigin(t,i,e,s,r){let n=t.x,o=t.y;const h=y(s)-y(i),l=y(r)-y(e);if(h||l){const t=this._getTransformedDimensions();n+=h*t.x,o+=l*t.y}return new a(n,o)}translateToCenterPoint(t,i,e){if(i===r&&e===r)return t;const s=this.translateToGivenOrigin(t,i,e,r,r);return this.angle?s.rotate(x(this.angle),t):s}translateToOriginPoint(t,i,e){const s=this.translateToGivenOrigin(t,r,r,i,e);return this.angle?s.rotate(x(this.angle),t):s}getCenterPoint(){const t=this.getRelativeCenterPoint();return this.group?c(t,this.group.calcTransformMatrix()):t}getRelativeCenterPoint(){return this.translateToCenterPoint(new a(this.left,this.top),this.originX,this.originY)}getPointByOrigin(t,i){return this.translateToOriginPoint(this.getRelativeCenterPoint(),t,i)}setPositionByOrigin(t,i,e){const s=this.translateToCenterPoint(t,i,e),r=this.translateToOriginPoint(s,this.originX,this.originY);this.set({left:r.x,top:r.y})}_getLeftTopCoords(){return this.translateToOriginPoint(this.getRelativeCenterPoint(),n,o)}}export{w as ObjectGeometry};
//# sourceMappingURL=ObjectGeometry.min.mjs.map
