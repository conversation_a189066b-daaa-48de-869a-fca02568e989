import{defineProperty as t,objectSpread2 as e,objectWithoutProperties as s}from"../../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{cache as i}from"../../cache.min.mjs";import{config as o}from"../../config.min.mjs";import{ALIASING_LIMIT as r,SCALE_X as n,SCALE_Y as a,STROKE as h,iMatrix as c,CENTER as l,VERSION as m,FILL as f,LEFT as d,TOP as p}from"../../constants.min.mjs";import{Point as u}from"../../Point.min.mjs";import{Shadow as g}from"../../Shadow.min.mjs";import{classRegistry as C}from"../../ClassRegistry.min.mjs";import{runningAnimations as w}from"../../util/animation/AnimationRegistry.min.mjs";import{capValue as v}from"../../util/misc/capValue.min.mjs";import{createCanvasElement as _,createCanvasElementFor as y,toDataURL as b,toBlob as k}from"../../util/misc/dom.min.mjs";import{qrDecompose as O,invertTransform as j}from"../../util/misc/matrix.min.mjs";import{enlivenObjectEnlivables as S}from"../../util/misc/objectEnlive.min.mjs";import{saveObjectTransform as P,resetObjectTransform as T}from"../../util/misc/objectTransforms.min.mjs";import{sendObjectToPlane as Y}from"../../util/misc/planeChange.min.mjs";import{pick as x,pickBy as X}from"../../util/misc/pick.min.mjs";import{toFixed as D}from"../../util/misc/toFixed.min.mjs";import{StaticCanvas as F}from"../../canvas/StaticCanvas.min.mjs";import{isFiller as z,isSerializableFiller as A}from"../../util/typeAssertions.min.mjs";import{stateProperties as L,cacheProperties as M,fabricObjectDefaultValues as R}from"./defaultValues.min.mjs";import{getDevicePixelRatio as B,getEnv as V}from"../../env/index.min.mjs";import{log as I}from"../../util/internals/console.min.mjs";import{animateColor as W,animate as E}from"../../util/animation/animate.min.mjs";import{ObjectGeometry as G}from"./ObjectGeometry.min.mjs";const N=["type"],U=["extraParam"];class J extends G{static getDefaults(){return J.ownDefaults}get type(){const t=this.constructor.type;return"FabricObject"===t?"object":t.toLowerCase()}set type(t){I("warn","Setting type has no effect",t)}constructor(e){super(),t(this,"_cacheContext",null),Object.assign(this,J.ownDefaults),this.setOptions(e)}_createCacheCanvas(){this._cacheCanvas=_(),this._cacheContext=this._cacheCanvas.getContext("2d"),this._updateCacheCanvas(),this.dirty=!0}_limitCacheSize(t){const e=t.width,s=t.height,r=o.maxCacheSideLimit,n=o.minCacheSideLimit;if(e<=r&&s<=r&&e*s<=o.perfLimitSizeTotal)return e<n&&(t.width=n),s<n&&(t.height=n),t;const a=e/s,[h,c]=i.limitDimsByArea(a),l=v(n,h,r),m=v(n,c,r);return e>l&&(t.zoomX/=e/l,t.width=l,t.capped=!0),s>m&&(t.zoomY/=s/m,t.height=m,t.capped=!0),t}_getCacheCanvasDimensions(){const t=this.getTotalObjectScaling(),e=this._getTransformedDimensions({skewX:0,skewY:0}),s=e.x*t.x/this.scaleX,i=e.y*t.y/this.scaleY;return{width:Math.ceil(s+r),height:Math.ceil(i+r),zoomX:t.x,zoomY:t.y,x:s,y:i}}_updateCacheCanvas(){const t=this._cacheCanvas,e=this._cacheContext,{width:s,height:i,zoomX:o,zoomY:r,x:n,y:a}=this._limitCacheSize(this._getCacheCanvasDimensions()),h=s!==t.width||i!==t.height,c=this.zoomX!==o||this.zoomY!==r;if(!t||!e)return!1;if(h||c){s!==t.width||i!==t.height?(t.width=s,t.height=i):(e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t.width,t.height));const h=n/2,c=a/2;return this.cacheTranslationX=Math.round(t.width/2-h)+h,this.cacheTranslationY=Math.round(t.height/2-c)+c,e.translate(this.cacheTranslationX,this.cacheTranslationY),e.scale(o,r),this.zoomX=o,this.zoomY=r,!0}return!1}setOptions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._setOptions(t)}transform(t){const e=this.group&&!this.group._transformDone||this.group&&this.canvas&&t===this.canvas.contextTop,s=this.calcTransformMatrix(!e);t.transform(s[0],s[1],s[2],s[3],s[4],s[5])}getObjectScaling(){if(!this.group)return new u(Math.abs(this.scaleX),Math.abs(this.scaleY));const t=O(this.calcTransformMatrix());return new u(Math.abs(t.scaleX),Math.abs(t.scaleY))}getTotalObjectScaling(){const t=this.getObjectScaling();if(this.canvas){const e=this.canvas.getZoom(),s=this.getCanvasRetinaScaling();return t.scalarMultiply(e*s)}return t}getObjectOpacity(){let t=this.opacity;return this.group&&(t*=this.group.getObjectOpacity()),t}_constrainScale(t){return Math.abs(t)<this.minScaleLimit?t<0?-this.minScaleLimit:this.minScaleLimit:0===t?1e-4:t}_set(t,e){t!==n&&t!==a||(e=this._constrainScale(e)),t===n&&e<0?(this.flipX=!this.flipX,e*=-1):"scaleY"===t&&e<0?(this.flipY=!this.flipY,e*=-1):"shadow"!==t||!e||e instanceof g||(e=new g(e));const s=this[t]!==e;return this[t]=e,s&&this.constructor.cacheProperties.includes(t)&&(this.dirty=!0),this.parent&&(this.dirty||s&&this.constructor.stateProperties.includes(t))&&this.parent._set("dirty",!0),this}isNotVisible(){return 0===this.opacity||!this.width&&!this.height&&0===this.strokeWidth||!this.visible}render(t){this.isNotVisible()||this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(t.save(),this._setupCompositeOperation(t),this.drawSelectionBackground(t),this.transform(t),this._setOpacity(t),this._setShadow(t),this.shouldCache()?(this.renderCache(),this.drawCacheOnCanvas(t)):(this._removeCacheCanvas(),this.drawObject(t,!1,{}),this.dirty=!1),t.restore())}drawSelectionBackground(t){}renderCache(t){if(t=t||{},this._cacheCanvas&&this._cacheContext||this._createCacheCanvas(),this.isCacheDirty()&&this._cacheContext){const{zoomX:e,zoomY:s,cacheTranslationX:i,cacheTranslationY:o}=this,{width:r,height:n}=this._cacheCanvas;this.drawObject(this._cacheContext,t.forClipping,{zoomX:e,zoomY:s,cacheTranslationX:i,cacheTranslationY:o,width:r,height:n,parentClipPaths:[]}),this.dirty=!1}}_removeCacheCanvas(){this._cacheCanvas=void 0,this._cacheContext=null}hasStroke(){return this.stroke&&"transparent"!==this.stroke&&0!==this.strokeWidth}hasFill(){return this.fill&&"transparent"!==this.fill}needsItsOwnCache(){return!!(this.paintFirst===h&&this.hasFill()&&this.hasStroke()&&this.shadow)||!!this.clipPath}shouldCache(){return this.ownCaching=this.objectCaching&&(!this.parent||!this.parent.isOnACache())||this.needsItsOwnCache(),this.ownCaching}willDrawShadow(){return!!this.shadow&&(0!==this.shadow.offsetX||0!==this.shadow.offsetY)}drawClipPathOnCache(t,e,s){t.save(),e.inverted?t.globalCompositeOperation="destination-out":t.globalCompositeOperation="destination-in",t.setTransform(1,0,0,1,0,0),t.drawImage(s,0,0),t.restore()}drawObject(t,e,s){const i=this.fill,o=this.stroke;e?(this.fill="black",this.stroke="",this._setClippingProperties(t)):this._renderBackground(t),this._render(t),this._drawClipPath(t,this.clipPath,s),this.fill=i,this.stroke=o}createClipPathLayer(t,e){const s=y(e),i=s.getContext("2d");if(i.translate(e.cacheTranslationX,e.cacheTranslationY),i.scale(e.zoomX,e.zoomY),t._cacheCanvas=s,e.parentClipPaths.forEach((t=>{t.transform(i)})),e.parentClipPaths.push(t),t.absolutePositioned){const t=j(this.calcTransformMatrix());i.transform(t[0],t[1],t[2],t[3],t[4],t[5])}return t.transform(i),t.drawObject(i,!0,e),s}_drawClipPath(t,e,s){if(!e)return;e._transformDone=!0;const i=this.createClipPathLayer(e,s);this.drawClipPathOnCache(t,e,i)}drawCacheOnCanvas(t){t.scale(1/this.zoomX,1/this.zoomY),t.drawImage(this._cacheCanvas,-this.cacheTranslationX,-this.cacheTranslationY)}isCacheDirty(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.isNotVisible())return!1;const e=this._cacheCanvas,s=this._cacheContext;return!(!e||!s||t||!this._updateCacheCanvas())||!!(this.dirty||this.clipPath&&this.clipPath.absolutePositioned)&&(e&&s&&!t&&(s.save(),s.setTransform(1,0,0,1,0,0),s.clearRect(0,0,e.width,e.height),s.restore()),!0)}_renderBackground(t){if(!this.backgroundColor)return;const e=this._getNonTransformedDimensions();t.fillStyle=this.backgroundColor,t.fillRect(-e.x/2,-e.y/2,e.x,e.y),this._removeShadow(t)}_setOpacity(t){this.group&&!this.group._transformDone?t.globalAlpha=this.getObjectOpacity():t.globalAlpha*=this.opacity}_setStrokeStyles(t,e){const s=e.stroke;s&&(t.lineWidth=e.strokeWidth,t.lineCap=e.strokeLineCap,t.lineDashOffset=e.strokeDashOffset,t.lineJoin=e.strokeLineJoin,t.miterLimit=e.strokeMiterLimit,z(s)?"percentage"===s.gradientUnits||s.gradientTransform||s.patternTransform?this._applyPatternForTransformedGradient(t,s):(t.strokeStyle=s.toLive(t),this._applyPatternGradientTransform(t,s)):t.strokeStyle=e.stroke)}_setFillStyles(t,e){let{fill:s}=e;s&&(z(s)?(t.fillStyle=s.toLive(t),this._applyPatternGradientTransform(t,s)):t.fillStyle=s)}_setClippingProperties(t){t.globalAlpha=1,t.strokeStyle="transparent",t.fillStyle="#000000"}_setLineDash(t,e){e&&0!==e.length&&t.setLineDash(e)}_setShadow(t){if(!this.shadow)return;const e=this.shadow,s=this.canvas,i=this.getCanvasRetinaScaling(),[r,,,n]=(null==s?void 0:s.viewportTransform)||c,a=r*i,h=n*i,l=e.nonScaling?new u(1,1):this.getObjectScaling();t.shadowColor=e.color,t.shadowBlur=e.blur*o.browserShadowBlurConstant*(a+h)*(l.x+l.y)/4,t.shadowOffsetX=e.offsetX*a*l.x,t.shadowOffsetY=e.offsetY*h*l.y}_removeShadow(t){this.shadow&&(t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0)}_applyPatternGradientTransform(t,e){if(!z(e))return{offsetX:0,offsetY:0};const s=e.gradientTransform||e.patternTransform,i=-this.width/2+e.offsetX||0,o=-this.height/2+e.offsetY||0;return"percentage"===e.gradientUnits?t.transform(this.width,0,0,this.height,i,o):t.transform(1,0,0,1,i,o),s&&t.transform(s[0],s[1],s[2],s[3],s[4],s[5]),{offsetX:i,offsetY:o}}_renderPaintInOrder(t){this.paintFirst===h?(this._renderStroke(t),this._renderFill(t)):(this._renderFill(t),this._renderStroke(t))}_render(t){}_renderFill(t){this.fill&&(t.save(),this._setFillStyles(t,this),"evenodd"===this.fillRule?t.fill("evenodd"):t.fill(),t.restore())}_renderStroke(t){if(this.stroke&&0!==this.strokeWidth){if(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this.strokeUniform){const e=this.getObjectScaling();t.scale(1/e.x,1/e.y)}this._setLineDash(t,this.strokeDashArray),this._setStrokeStyles(t,this),t.stroke(),t.restore()}}_applyPatternForTransformedGradient(t,e){var s;const i=this._limitCacheSize(this._getCacheCanvasDimensions()),o=this.getCanvasRetinaScaling(),r=i.x/this.scaleX/o,n=i.y/this.scaleY/o,a=y({width:Math.ceil(r),height:Math.ceil(n)}),h=a.getContext("2d");h&&(h.beginPath(),h.moveTo(0,0),h.lineTo(r,0),h.lineTo(r,n),h.lineTo(0,n),h.closePath(),h.translate(r/2,n/2),h.scale(i.zoomX/this.scaleX/o,i.zoomY/this.scaleY/o),this._applyPatternGradientTransform(h,e),h.fillStyle=e.toLive(t),h.fill(),t.translate(-this.width/2-this.strokeWidth/2,-this.height/2-this.strokeWidth/2),t.scale(o*this.scaleX/i.zoomX,o*this.scaleY/i.zoomY),t.strokeStyle=null!==(s=h.createPattern(a,"no-repeat"))&&void 0!==s?s:"")}_findCenterFromElement(){return new u(this.left+this.width/2,this.top+this.height/2)}clone(t){const e=this.toObject(t);return this.constructor.fromObject(e)}cloneAsImage(t){const e=this.toCanvasElement(t);return new(C.getClass("image"))(e)}toCanvasElement(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const e=P(this),s=this.group,i=this.shadow,o=Math.abs,r=t.enableRetinaScaling?B():1,n=(t.multiplier||1)*r,a=t.canvasProvider||(t=>new F(t,{enableRetinaScaling:!1,renderOnAddRemove:!1,skipOffscreen:!1}));delete this.group,t.withoutTransform&&T(this),t.withoutShadow&&(this.shadow=null),t.viewportTransform&&Y(this,this.getViewportTransform()),this.setCoords();const h=_(),c=this.getBoundingRect(),m=this.shadow,f=new u;if(m){const t=m.blur,e=m.nonScaling?new u(1,1):this.getObjectScaling();f.x=2*Math.round(o(m.offsetX)+t)*o(e.x),f.y=2*Math.round(o(m.offsetY)+t)*o(e.y)}const d=c.width+f.x,p=c.height+f.y;h.width=Math.ceil(d),h.height=Math.ceil(p);const g=a(h);"jpeg"===t.format&&(g.backgroundColor="#fff"),this.setPositionByOrigin(new u(g.width/2,g.height/2),l,l);const C=this.canvas;g._objects=[this],this.set("canvas",g),this.setCoords();const w=g.toCanvasElement(n||1,t);return this.set("canvas",C),this.shadow=i,s&&(this.group=s),this.set(e),this.setCoords(),g._objects=[],g.destroy(),w}toDataURL(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return b(this.toCanvasElement(t),t.format||"png",t.quality||1)}toBlob(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return k(this.toCanvasElement(t),t.format||"png",t.quality||1)}isType(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];return e.includes(this.constructor.type)||e.includes(this.type)}complexity(){return 1}toJSON(){return this.toObject()}rotate(t){const{centeredRotation:e,originX:s,originY:i}=this;if(e){const{x:t,y:e}=this.getRelativeCenterPoint();this.originX=l,this.originY=l,this.left=t,this.top=e}if(this.set("angle",t),e){const{x:t,y:e}=this.translateToOriginPoint(this.getRelativeCenterPoint(),s,i);this.left=t,this.top=e,this.originX=s,this.originY=i}}setOnGroup(){}_setupCompositeOperation(t){this.globalCompositeOperation&&(t.globalCompositeOperation=this.globalCompositeOperation)}dispose(){w.cancelByTarget(this),this.off(),this._set("canvas",void 0),this._cacheCanvas&&V().dispose(this._cacheCanvas),this._cacheCanvas=void 0,this._cacheContext=null}animate(t,e){return Object.entries(t).reduce(((t,s)=>{let[i,o]=s;return t[i]=this._animate(i,o,e),t}),{})}_animate(t,s){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const o=t.split("."),r=this.constructor.colorProperties.includes(o[o.length-1]),{abort:n,startValue:a,onChange:h,onComplete:c}=i,l=e(e({},i),{},{target:this,startValue:null!=a?a:o.reduce(((t,e)=>t[e]),this),endValue:s,abort:null==n?void 0:n.bind(this),onChange:(t,e,s)=>{o.reduce(((e,s,i)=>(i===o.length-1&&(e[s]=t),e[s])),this),h&&h(t,e,s)},onComplete:(t,e,s)=>{this.setCoords(),c&&c(t,e,s)}});return r?W(l):E(l)}isDescendantOf(t){const{parent:e,group:s}=this;return e===t||s===t||!!e&&e.isDescendantOf(t)||!!s&&s!==e&&s.isDescendantOf(t)}getAncestors(){const t=[];let e=this;do{e=e.parent,e&&t.push(e)}while(e);return t}findCommonAncestors(t){if(this===t)return{fork:[],otherFork:[],common:[this,...this.getAncestors()]};const e=this.getAncestors(),s=t.getAncestors();if(0===e.length&&s.length>0&&this===s[s.length-1])return{fork:[],otherFork:[t,...s.slice(0,s.length-1)],common:[this]};for(let i,o=0;o<e.length;o++){if(i=e[o],i===t)return{fork:[this,...e.slice(0,o)],otherFork:[],common:e.slice(o)};for(let r=0;r<s.length;r++){if(this===s[r])return{fork:[],otherFork:[t,...s.slice(0,r)],common:[this,...e]};if(i===s[r])return{fork:[this,...e.slice(0,o)],otherFork:[t,...s.slice(0,r)],common:e.slice(o)}}}return{fork:[this,...e],otherFork:[t,...s],common:[]}}hasCommonAncestors(t){const e=this.findCommonAncestors(t);return e&&!!e.common.length}isInFrontOf(t){if(this===t)return;const e=this.findCommonAncestors(t);if(e.fork.includes(t))return!0;if(e.otherFork.includes(this))return!1;const s=e.common[0]||this.canvas;if(!s)return;const i=e.fork.pop(),o=e.otherFork.pop(),r=s._objects.indexOf(i),n=s._objects.indexOf(o);return r>-1&&r>n}toObject(){const t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).concat(J.customProperties,this.constructor.customProperties||[]);let s;const i=o.NUM_FRACTION_DIGITS,{clipPath:r,fill:n,stroke:a,shadow:h,strokeDashArray:c,left:l,top:f,originX:d,originY:p,width:u,height:g,strokeWidth:C,strokeLineCap:w,strokeDashOffset:v,strokeLineJoin:_,strokeUniform:y,strokeMiterLimit:b,scaleX:k,scaleY:O,angle:j,flipX:S,flipY:P,opacity:T,visible:Y,backgroundColor:X,fillRule:F,paintFirst:z,globalCompositeOperation:L,skewX:M,skewY:R}=this;r&&!r.excludeFromExport&&(s=r.toObject(t.concat("inverted","absolutePositioned")));const B=t=>D(t,i),V=e(e({},x(this,t)),{},{type:this.constructor.type,version:m,originX:d,originY:p,left:B(l),top:B(f),width:B(u),height:B(g),fill:A(n)?n.toObject():n,stroke:A(a)?a.toObject():a,strokeWidth:B(C),strokeDashArray:c?c.concat():c,strokeLineCap:w,strokeDashOffset:v,strokeLineJoin:_,strokeUniform:y,strokeMiterLimit:B(b),scaleX:B(k),scaleY:B(O),angle:B(j),flipX:S,flipY:P,opacity:B(T),shadow:h?h.toObject():h,visible:Y,backgroundColor:X,fillRule:F,paintFirst:z,globalCompositeOperation:L,skewX:B(M),skewY:B(R)},s?{clipPath:s}:null);return this.includeDefaultValues?V:this._removeDefaultValues(V)}toDatalessObject(t){return this.toObject(t)}_removeDefaultValues(t){const e=this.constructor.getDefaults(),s=Object.keys(e).length>0?e:Object.getPrototypeOf(this);return X(t,((t,e)=>{if(e===d||e===p||"type"===e)return!0;const i=s[e];return t!==i&&!(Array.isArray(t)&&Array.isArray(i)&&0===t.length&&0===i.length)}))}toString(){return"#<".concat(this.constructor.type,">")}static _fromObject(t){let e=s(t,N),i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{extraParam:o}=i,r=s(i,U);return S(e,r).then((t=>o?(delete t[o],new this(e[o],t)):new this(t)))}static fromObject(t,e){return this._fromObject(t,e)}}t(J,"stateProperties",L),t(J,"cacheProperties",M),t(J,"ownDefaults",R),t(J,"type","FabricObject"),t(J,"colorProperties",[f,h,"backgroundColor"]),t(J,"customProperties",[]),C.setClass(J),C.setClass(J,"object");export{J as FabricObject};
//# sourceMappingURL=Object.min.mjs.map
