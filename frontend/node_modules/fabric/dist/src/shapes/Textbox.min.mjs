import{defineProperty as t,objectSpread2 as e}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{IText as s}from"./IText/IText.min.mjs";import{classRegistry as i}from"../ClassRegistry.min.mjs";import{createTextboxDefaultControls as n}from"../controls/commonControls.min.mjs";import{JUSTIFY as r}from"./Text/constants.min.mjs";const l={minWidth:20,dynamicMinWidth:2,lockScalingFlip:!0,noScaleCache:!1,_wordJoiners:/[ \t\r]/,splitByGrapheme:!1};class h extends s{static getDefaults(){return e(e({},super.getDefaults()),h.ownDefaults)}constructor(t,s){super(t,e(e({},h.ownDefaults),s))}static createControls(){return{controls:n()}}initDimensions(){this.initialized&&(this.isEditing&&this.initDelayedCursor(),this._clearCache(),this.dynamicMinWidth=0,this._styleMap=this._generateStyleMap(this._splitText()),this.dynamicMinWidth>this.width&&this._set("width",this.dynamicMinWidth),this.textAlign.includes(r)&&this.enlargeSpaces(),this.height=this.calcTextHeight())}_generateStyleMap(t){let e=0,s=0,i=0;const n={};for(let r=0;r<t.graphemeLines.length;r++)"\n"===t.graphemeText[i]&&r>0?(s=0,i++,e++):!this.splitByGrapheme&&this._reSpaceAndTab.test(t.graphemeText[i])&&r>0&&(s++,i++),n[r]={line:e,offset:s},i+=t.graphemeLines[r].length,s+=t.graphemeLines[r].length;return n}styleHas(t,e){if(this._styleMap&&!this.isWrapping){const t=this._styleMap[e];t&&(e=t.line)}return super.styleHas(t,e)}isEmptyStyles(t){if(!this.styles)return!0;let e,s=0,i=t+1,n=!1;const r=this._styleMap[t],l=this._styleMap[t+1];r&&(t=r.line,s=r.offset),l&&(i=l.line,n=i===t,e=l.offset);const h=void 0===t?this.styles:{line:this.styles[t]};for(const t in h)for(const i in h[t]){const r=parseInt(i,10);if(r>=s&&(!n||r<e))for(const e in h[t][i])return!1}return!0}_getStyleDeclaration(t,e){if(this._styleMap&&!this.isWrapping){const s=this._styleMap[t];if(!s)return{};t=s.line,e=s.offset+e}return super._getStyleDeclaration(t,e)}_setStyleDeclaration(t,e,s){const i=this._styleMap[t];super._setStyleDeclaration(i.line,i.offset+e,s)}_deleteStyleDeclaration(t,e){const s=this._styleMap[t];super._deleteStyleDeclaration(s.line,s.offset+e)}_getLineStyle(t){const e=this._styleMap[t];return!!this.styles[e.line]}_setLineStyle(t){const e=this._styleMap[t];super._setLineStyle(e.line)}_wrapText(t,e){this.isWrapping=!0;const s=this.getGraphemeDataForRender(t),i=[];for(let t=0;t<s.wordsData.length;t++)i.push(...this._wrapLine(t,e,s));return this.isWrapping=!1,i}getGraphemeDataForRender(t){const e=this.splitByGrapheme,s=e?"":" ";let i=0;return{wordsData:t.map(((t,n)=>{let r=0;const l=e?this.graphemeSplit(t):this.wordSplit(t);return 0===l.length?[{word:[],width:0}]:l.map((t=>{const l=e?[t]:this.graphemeSplit(t),h=this._measureWord(l,n,r);return i=Math.max(h,i),r+=l.length+s.length,{word:l,width:h}}))})),largestWordWidth:i}}_measureWord(t,e){let s,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=0;for(let r=0,l=t.length;r<l;r++){n+=this._getGraphemeBox(t[r],e,r+i,s,true).kernedWidth,s=t[r]}return n}wordSplit(t){return t.split(this._wordJoiners)}_wrapLine(t,e,s){let{largestWordWidth:i,wordsData:n}=s,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;const l=this._getWidthOfCharSpacing(),h=this.splitByGrapheme,a=[],o=h?"":" ";let p=0,c=[],d=0,m=0,y=!0;e-=r;const g=Math.max(e,i,this.dynamicMinWidth),u=n[t];let _;for(d=0,_=0;_<u.length;_++){const{word:e,width:s}=u[_];d+=e.length,p+=m+s-l,p>g&&!y?(a.push(c),c=[],p=s,y=!0):p+=l,y||h||c.push(o),c=c.concat(e),m=h?0:this._measureWord([o],t,d),d++,y=!1}return _&&a.push(c),i+r>this.dynamicMinWidth&&(this.dynamicMinWidth=i-l+r),a}isEndOfWrapping(t){return!this._styleMap[t+1]||this._styleMap[t+1].line!==this._styleMap[t].line}missingNewlineOffset(t,e){return this.splitByGrapheme&&!e?this.isEndOfWrapping(t)?1:0:1}_splitTextIntoLines(t){const e=super._splitTextIntoLines(t),s=this._wrapText(e.lines,this.width),i=new Array(s.length);for(let t=0;t<s.length;t++)i[t]=s[t].join("");return e.lines=i,e.graphemeLines=s,e}getMinWidth(){return Math.max(this.minWidth,this.dynamicMinWidth)}_removeExtraneousStyles(){const t=new Map;for(const e in this._styleMap){const s=parseInt(e,10);if(this._textLines[s]){const s=this._styleMap[e].line;t.set("".concat(s),!0)}}for(const e in this.styles)t.has(e)||delete this.styles[e]}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return super.toObject(["minWidth","splitByGrapheme",...t])}}t(h,"type","Textbox"),t(h,"textLayoutProperties",[...s.textLayoutProperties,"width"]),t(h,"ownDefaults",l),i.setClass(h);export{h as Textbox,l as textboxDefaultValues};
//# sourceMappingURL=Textbox.min.mjs.map
