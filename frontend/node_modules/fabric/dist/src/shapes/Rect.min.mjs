import{defineProperty as t,objectSpread2 as e,objectWithoutProperties as r}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{kRect as i}from"../constants.min.mjs";import{SHARED_ATTRIBUTES as s}from"../parser/attributes.min.mjs";import{parseAttributes as o}from"../parser/parseAttributes.min.mjs";import{classRegistry as n}from"../ClassRegistry.min.mjs";import{FabricObject as h}from"./Object/FabricObject.min.mjs";import{cacheProperties as a}from"./Object/defaultValues.min.mjs";const c=["left","top","width","height","visible"],m={rx:0,ry:0},l=["rx","ry"];class p extends h{static getDefaults(){return e(e({},super.getDefaults()),p.ownDefaults)}constructor(t){super(),Object.assign(this,p.ownDefaults),this.setOptions(t),this._initRxRy()}_initRxRy(){const{rx:t,ry:e}=this;t&&!e?this.ry=t:e&&!t&&(this.rx=e)}_render(t){const{width:e,height:r}=this,s=-e/2,o=-r/2,n=this.rx?Math.min(this.rx,e/2):0,h=this.ry?Math.min(this.ry,r/2):0,a=0!==n||0!==h;t.beginPath(),t.moveTo(s+n,o),t.lineTo(s+e-n,o),a&&t.bezierCurveTo(s+e-i*n,o,s+e,o+i*h,s+e,o+h),t.lineTo(s+e,o+r-h),a&&t.bezierCurveTo(s+e,o+r-i*h,s+e-i*n,o+r,s+e-n,o+r),t.lineTo(s+n,o+r),a&&t.bezierCurveTo(s+i*n,o+r,s,o+r-i*h,s,o+r-h),t.lineTo(s,o+h),a&&t.bezierCurveTo(s,o+i*h,s+i*n,o,s+n,o),t.closePath(),this._renderPaintInOrder(t)}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return super.toObject([...l,...t])}_toSVG(){const{width:t,height:e,rx:r,ry:i}=this;return["<rect ","COMMON_PARTS",'x="'.concat(-t/2,'" y="').concat(-e/2,'" rx="').concat(r,'" ry="').concat(i,'" width="').concat(t,'" height="').concat(e,'" />\n')]}static async fromElement(t,i,s){const n=o(t,this.ATTRIBUTE_NAMES,s),{left:h=0,top:a=0,width:m=0,height:l=0,visible:p=!0}=n,u=r(n,c);return new this(e(e(e({},i),u),{},{left:h,top:a,width:m,height:l,visible:Boolean(p&&m&&l)}))}}t(p,"type","Rect"),t(p,"cacheProperties",[...a,...l]),t(p,"ownDefaults",m),t(p,"ATTRIBUTE_NAMES",[...s,"x","y","rx","ry","width","height"]),n.setClass(p),n.setSVGClass(p);export{p as Rect,m as rectDefaultValues};
//# sourceMappingURL=Rect.min.mjs.map
