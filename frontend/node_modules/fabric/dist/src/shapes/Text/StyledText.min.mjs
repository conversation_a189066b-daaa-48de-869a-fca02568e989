import{defineProperty as t,objectSpread2 as e}from"../../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{FabricObject as s}from"../Object/FabricObject.min.mjs";import{styleProperties as i}from"./constants.min.mjs";import"../../util/misc/vectors.min.mjs";import"../../Point.min.mjs";import"../../util/misc/projectStroke/StrokeLineJoinProjections.min.mjs";import"../../config.min.mjs";import"../Group.min.mjs";import{pickBy as n,pick as o}from"../../util/misc/pick.min.mjs";import"../../cache.min.mjs";import"../../parser/constants.min.mjs";import"../../util/animation/AnimationRegistry.min.mjs";class r extends s{isEmptyStyles(t){if(!this.styles)return!0;if(void 0!==t&&!this.styles[t])return!0;const e=void 0===t?this.styles:{line:this.styles[t]};for(const t in e)for(const s in e[t])for(const i in e[t][s])return!1;return!0}styleHas(t,e){if(!this.styles)return!1;if(void 0!==e&&!this.styles[e])return!1;const s=void 0===e?this.styles:{0:this.styles[e]};for(const e in s)for(const i in s[e])if(void 0!==s[e][i][t])return!0;return!1}cleanStyle(t){if(!this.styles)return!1;const e=this.styles;let s,i,n=0,o=!0,r=0;for(const r in e){s=0;for(const l in e[r]){const c=e[r][l]||{};n++,void 0!==c[t]?(i?c[t]!==i&&(o=!1):i=c[t],c[t]===this[t]&&delete c[t]):o=!1,0!==Object.keys(c).length?s++:delete e[r][l]}0===s&&delete e[r]}for(let t=0;t<this._textLines.length;t++)r+=this._textLines[t].length;o&&n===r&&(this[t]=i,this.removeStyle(t))}removeStyle(t){if(!this.styles)return;const e=this.styles;let s,i,n;for(i in e){for(n in s=e[i],s)delete s[n][t],0===Object.keys(s[n]).length&&delete s[n];0===Object.keys(s).length&&delete e[i]}}_extendStyles(t,s){const{lineIndex:i,charIndex:o}=this.get2DCursorLocation(t);this._getLineStyle(i)||this._setLineStyle(i);const r=n(e(e({},this._getStyleDeclaration(i,o)),s),(t=>void 0!==t));this._setStyleDeclaration(i,o,r)}getSelectionStyles(t,e,s){const i=[];for(let n=t;n<(e||t);n++)i.push(this.getStyleAtPosition(n,s));return i}getStyleAtPosition(t,e){const{lineIndex:s,charIndex:i}=this.get2DCursorLocation(t);return e?this.getCompleteStyleDeclaration(s,i):this._getStyleDeclaration(s,i)}setSelectionStyles(t,e,s){for(let i=e;i<(s||e);i++)this._extendStyles(i,t);this._forceClearCache=!0}_getStyleDeclaration(t,e){var s;const i=this.styles&&this.styles[t];return i&&null!==(s=i[e])&&void 0!==s?s:{}}getCompleteStyleDeclaration(t,s){return e(e({},o(this,this.constructor._styleProperties)),this._getStyleDeclaration(t,s))}_setStyleDeclaration(t,e,s){this.styles[t][e]=s}_deleteStyleDeclaration(t,e){delete this.styles[t][e]}_getLineStyle(t){return!!this.styles[t]}_setLineStyle(t){this.styles[t]={}}_deleteLineStyle(t){delete this.styles[t]}}t(r,"_styleProperties",i);export{r as StyledText};
//# sourceMappingURL=StyledText.min.mjs.map
