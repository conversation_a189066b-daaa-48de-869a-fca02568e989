import{config as t}from"../../config.min.mjs";import{escapeXml as e}from"../../util/lang_string.min.mjs";import{colorPropToSVG as i,createSVGRect as n}from"../../util/misc/svgParsing.min.mjs";import{hasStyleChanged as o}from"../../util/misc/textStyles.min.mjs";import{toFixed as s}from"../../util/misc/toFixed.min.mjs";import{FabricObjectSVGExportMixin as r}from"../Object/FabricObjectSVGExportMixin.min.mjs";import{JUSTIFY as h}from"./constants.min.mjs";import{STROKE as c,FILL as a}from"../../constants.min.mjs";import{createRotateMatrix as l}from"../../util/misc/matrix.min.mjs";import{radiansToDegrees as g}from"../../util/misc/radiansDegreesConversion.min.mjs";import{Point as f}from"../../Point.min.mjs";import{matrixToSVG as m}from"../../util/misc/svgExport.min.mjs";const d=/  +/g,S=/"/g;function p(t,e,i,o,s){return"\t\t".concat(n(t,{left:e,top:i,width:o,height:s}),"\n")}class x extends r{_toSVG(){const t=this._getSVGLeftTopOffsets(),e=this._getSVGTextAndBg(t.textTop,t.textLeft);return this._wrapSVGTextAndBg(e)}toSVG(t){const e=this._createBaseSVGMarkup(this._toSVG(),{reviver:t,noStyle:!0,withShadow:!0}),i=this.path;return i?e+i._createBaseSVGMarkup(i._toSVG(),{reviver:t,withShadow:!0,additionalTransform:m(this.calcOwnMatrix())}):e}_getSVGLeftTopOffsets(){return{textLeft:-this.width/2,textTop:-this.height/2,lineTop:this.getHeightOfLine(0)}}_wrapSVGTextAndBg(t){let{textBgRects:e,textSpans:i}=t;const n=this.getSvgTextDecoration(this);return[e.join(""),'\t\t<text xml:space="preserve" ','font-family="'.concat(this.fontFamily.replace(S,"'"),'" '),'font-size="'.concat(this.fontSize,'" '),this.fontStyle?'font-style="'.concat(this.fontStyle,'" '):"",this.fontWeight?'font-weight="'.concat(this.fontWeight,'" '):"",n?'text-decoration="'.concat(n,'" '):"","rtl"===this.direction?'direction="'.concat(this.direction,'" '):"",'style="',this.getSvgStyles(!0),'"',this.addPaintOrder()," >",i.join(""),"</text>\n"]}_getSVGTextAndBg(t,e){const i=[],n=[];let o,s=t;this.backgroundColor&&n.push(...p(this.backgroundColor,-this.width/2,-this.height/2,this.width,this.height));for(let t=0,r=this._textLines.length;t<r;t++)o=this._getLineLeftOffset(t),"rtl"===this.direction&&(o+=this.width),(this.textBackgroundColor||this.styleHas("textBackgroundColor",t))&&this._setSVGTextLineBg(n,t,e+o,s),this._setSVGTextLineText(i,t,e+o,s),s+=this.getHeightOfLine(t);return{textSpans:i,textBgRects:n}}_createTextCharSpan(i,n,o,r,h){const c=t.NUM_FRACTION_DIGITS,a=this.getSvgSpanStyles(n,i!==i.trim()||!!i.match(d)),m=a?'style="'.concat(a,'"'):"",S=n.deltaY,p=S?' dy="'.concat(s(S,c),'" '):"",{angle:x,renderLeft:u,renderTop:_,width:T}=h;let y="";if(void 0!==u){const t=T/2;x&&(y=' rotate="'.concat(s(g(x),c),'"'));const e=l({angle:g(x)});e[4]=u,e[5]=_;const i=new f(-t,0).transform(e);o=i.x,r=i.y}return'<tspan x="'.concat(s(o,c),'" y="').concat(s(r,c),'" ').concat(p).concat(y).concat(m,">").concat(e(i),"</tspan>")}_setSVGTextLineText(t,e,i,n){const s=this.getHeightOfLine(e),r=this.textAlign.includes(h),c=this._textLines[e];let a,l,g,f,m,d="",S=0;n+=s*(1-this._fontSizeFraction)/this.lineHeight;for(let s=0,h=c.length-1;s<=h;s++)m=s===h||this.charSpacing||this.path,d+=c[s],g=this.__charBounds[e][s],0===S?(i+=g.kernedWidth-g.width,S+=g.width):S+=g.kernedWidth,r&&!m&&this._reSpaceAndTab.test(c[s])&&(m=!0),m||(a=a||this.getCompleteStyleDeclaration(e,s),l=this.getCompleteStyleDeclaration(e,s+1),m=o(a,l,!0)),m&&(f=this._getStyleDeclaration(e,s),t.push(this._createTextCharSpan(d,f,i,n,g)),d="",a=l,"rtl"===this.direction?i-=S:i+=S,S=0)}_setSVGTextLineBg(t,e,i,n){const o=this._textLines[e],s=this.getHeightOfLine(e)/this.lineHeight;let r,h=0,c=0,a=this.getValueOfPropertyAt(e,0,"textBackgroundColor");for(let l=0;l<o.length;l++){const{left:o,width:g,kernedWidth:f}=this.__charBounds[e][l];r=this.getValueOfPropertyAt(e,l,"textBackgroundColor"),r!==a?(a&&t.push(...p(a,i+c,n,h,s)),c=o,h=g,a=r):h+=f}r&&t.push(...p(a,i+c,n,h,s))}_getSVGLineTopOffset(t){let e,i=0;for(e=0;e<t;e++)i+=this.getHeightOfLine(e);const n=this.getHeightOfLine(e);return{lineTop:i,offset:(this._fontSizeMult-this._fontSizeFraction)*n/(this.lineHeight*this._fontSizeMult)}}getSvgStyles(e){return"".concat(super.getSvgStyles(e)," text-decoration-thickness: ").concat(s(this.textDecorationThickness*this.getObjectScaling().y/10,t.NUM_FRACTION_DIGITS),"%; white-space: pre;")}getSvgSpanStyles(e,n){const{fontFamily:o,strokeWidth:r,stroke:h,fill:l,fontSize:g,fontStyle:f,fontWeight:m,deltaY:d,textDecorationThickness:S,linethrough:p,overline:x,underline:u}=e,_=this.getSvgTextDecoration({underline:null!=u?u:this.underline,overline:null!=x?x:this.overline,linethrough:null!=p?p:this.linethrough}),T=S||this.textDecorationThickness;return[h?i(c,h):"",r?"stroke-width: ".concat(r,"; "):"",o?"font-family: ".concat(o.includes("'")||o.includes('"')?o:"'".concat(o,"'"),"; "):"",g?"font-size: ".concat(g,"px; "):"",f?"font-style: ".concat(f,"; "):"",m?"font-weight: ".concat(m,"; "):"",_?"text-decoration: ".concat(_,"; text-decoration-thickness: ").concat(s(T*this.getObjectScaling().y/10,t.NUM_FRACTION_DIGITS),"%; "):"",l?i(a,l):"",d?"baseline-shift: ".concat(-d,"; "):"",n?"white-space: pre; ":""].join("")}getSvgTextDecoration(t){return["overline","underline","line-through"].filter((e=>t[e.replace("-","")])).join(" ")}}export{x as TextSVGExportMixin};
//# sourceMappingURL=TextSVGExportMixin.min.mjs.map
