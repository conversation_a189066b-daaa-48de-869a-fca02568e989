import{defineProperty as t,objectWithoutProperties as e,objectSpread2 as s}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{config as r}from"../config.min.mjs";import{SHARED_ATTRIBUTES as i}from"../parser/attributes.min.mjs";import{parseAttributes as o}from"../parser/parseAttributes.min.mjs";import{Point as a}from"../Point.min.mjs";import{makeBoundingBoxFromPoints as n}from"../util/misc/boundingBoxFromPoints.min.mjs";import{toFixed as h}from"../util/misc/toFixed.min.mjs";import{makePathSimpler as c,parsePath as m,joinPath as p,getBoundsOfCurve as f}from"../util/path/index.min.mjs";import{classRegistry as l}from"../ClassRegistry.min.mjs";import{FabricObject as u}from"./Object/FabricObject.min.mjs";import{LEFT as d,TOP as _,CENTER as b}from"../constants.min.mjs";import{cacheProperties as O}from"./Object/defaultValues.min.mjs";const P=["path","left","top"],j=["d"];class g extends u{constructor(t){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{path:r,left:i,top:o}=s,a=e(s,P);super(),Object.assign(this,g.ownDefaults),this.setOptions(a),this._setPath(t||[],!0),"number"==typeof i&&this.set(d,i),"number"==typeof o&&this.set(_,o)}_setPath(t,e){this.path=c(Array.isArray(t)?t:m(t)),this.setBoundingBox(e)}_findCenterFromElement(){const t=this._calcBoundsFromPath();return new a(t.left+t.width/2,t.top+t.height/2)}_renderPathCommands(t){const e=-this.pathOffset.x,s=-this.pathOffset.y;t.beginPath();for(const r of this.path)switch(r[0]){case"L":t.lineTo(r[1]+e,r[2]+s);break;case"M":t.moveTo(r[1]+e,r[2]+s);break;case"C":t.bezierCurveTo(r[1]+e,r[2]+s,r[3]+e,r[4]+s,r[5]+e,r[6]+s);break;case"Q":t.quadraticCurveTo(r[1]+e,r[2]+s,r[3]+e,r[4]+s);break;case"Z":t.closePath()}}_render(t){this._renderPathCommands(t),this._renderPaintInOrder(t)}toString(){return"#<Path (".concat(this.complexity(),'): { "top": ').concat(this.top,', "left": ').concat(this.left," }>")}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return s(s({},super.toObject(t)),{},{path:this.path.map((t=>t.slice()))})}toDatalessObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];const e=this.toObject(t);return this.sourcePath&&(delete e.path,e.sourcePath=this.sourcePath),e}_toSVG(){const t=p(this.path,r.NUM_FRACTION_DIGITS);return["<path ","COMMON_PARTS",'d="'.concat(t,'" stroke-linecap="round" />\n')]}_getOffsetTransform(){const t=r.NUM_FRACTION_DIGITS;return" translate(".concat(h(-this.pathOffset.x,t),", ").concat(h(-this.pathOffset.y,t),")")}toClipPathSVG(t){const e=this._getOffsetTransform();return"\t"+this._createBaseClipPathSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})}toSVG(t){const e=this._getOffsetTransform();return this._createBaseSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})}complexity(){return this.path.length}setDimensions(){this.setBoundingBox()}setBoundingBox(t){const{width:e,height:s,pathOffset:r}=this._calcDimensions();this.set({width:e,height:s,pathOffset:r}),t&&this.setPositionByOrigin(r,b,b)}_calcBoundsFromPath(){const t=[];let e=0,s=0,r=0,i=0;for(const o of this.path)switch(o[0]){case"L":r=o[1],i=o[2],t.push({x:e,y:s},{x:r,y:i});break;case"M":r=o[1],i=o[2],e=r,s=i;break;case"C":t.push(...f(r,i,o[1],o[2],o[3],o[4],o[5],o[6])),r=o[5],i=o[6];break;case"Q":t.push(...f(r,i,o[1],o[2],o[1],o[2],o[3],o[4])),r=o[3],i=o[4];break;case"Z":r=e,i=s}return n(t)}_calcDimensions(){const t=this._calcBoundsFromPath();return s(s({},t),{},{pathOffset:new a(t.left+t.width/2,t.top+t.height/2)})}static fromObject(t){return this._fromObject(t,{extraParam:"path"})}static async fromElement(t,r,i){const a=o(t,this.ATTRIBUTE_NAMES,i),{d:n}=a,h=e(a,j);return new this(n,s(s(s({},h),r),{},{left:void 0,top:void 0}))}}t(g,"type","Path"),t(g,"cacheProperties",[...O,"path","fillRule"]),t(g,"ATTRIBUTE_NAMES",[...i,"d"]),l.setClass(g),l.setSVGClass(g);export{g as Path};
//# sourceMappingURL=Path.min.mjs.map
