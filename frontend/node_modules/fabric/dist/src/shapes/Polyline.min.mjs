import{defineProperty as t,objectSpread2 as s,objectWithoutProperties as i}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{config as e}from"../config.min.mjs";import{SHARED_ATTRIBUTES as o}from"../parser/attributes.min.mjs";import{parseAttributes as n}from"../parser/parseAttributes.min.mjs";import{parsePointsAttribute as r}from"../parser/parsePointsAttribute.min.mjs";import{Point as h}from"../Point.min.mjs";import{classRegistry as m}from"../ClassRegistry.min.mjs";import{makeBoundingBoxFromPoints as a}from"../util/misc/boundingBoxFromPoints.min.mjs";import{calcDimensionsMatrix as p,transformPoint as l}from"../util/misc/matrix.min.mjs";import{projectStrokeOnPoints as c}from"../util/misc/projectStroke/index.min.mjs";import{degreesToRadians as f}from"../util/misc/radiansDegreesConversion.min.mjs";import{toFixed as u}from"../util/misc/toFixed.min.mjs";import{FabricObject as d}from"./Object/FabricObject.min.mjs";import{LEFT as g,TOP as k,CENTER as w,SCALE_X as x,SCALE_Y as O,SKEW_X as j,SKEW_Y as y}from"../constants.min.mjs";import{cacheProperties as B}from"./Object/defaultValues.min.mjs";const b=["left","top"],_={exactBoundingBox:!1};class D extends d{static getDefaults(){return s(s({},super.getDefaults()),D.ownDefaults)}constructor(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),t(this,"strokeDiff",void 0),Object.assign(this,D.ownDefaults),this.setOptions(i),this.points=s;const{left:e,top:o}=i;this.initialized=!0,this.setBoundingBox(!0),"number"==typeof e&&this.set(g,e),"number"==typeof o&&this.set(k,o)}isOpen(){return!0}_projectStrokeOnPoints(t){return c(this.points,t,this.isOpen())}_calcDimensions(t){t=s({scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,strokeLineCap:this.strokeLineCap,strokeLineJoin:this.strokeLineJoin,strokeMiterLimit:this.strokeMiterLimit,strokeUniform:this.strokeUniform,strokeWidth:this.strokeWidth},t||{});const i=this.exactBoundingBox?this._projectStrokeOnPoints(t).map((t=>t.projectedPoint)):this.points;if(0===i.length)return{left:0,top:0,width:0,height:0,pathOffset:new h,strokeOffset:new h,strokeDiff:new h};const e=a(i),o=p(s(s({},t),{},{scaleX:1,scaleY:1})),n=a(this.points.map((t=>l(t,o,!0)))),r=new h(this.scaleX,this.scaleY);let m=e.left+e.width/2,c=e.top+e.height/2;return this.exactBoundingBox&&(m-=c*Math.tan(f(this.skewX)),c-=m*Math.tan(f(this.skewY))),s(s({},e),{},{pathOffset:new h(m,c),strokeOffset:new h(n.left,n.top).subtract(new h(e.left,e.top)).multiply(r),strokeDiff:new h(e.width,e.height).subtract(new h(n.width,n.height)).multiply(r)})}_findCenterFromElement(){const t=a(this.points);return new h(t.left+t.width/2,t.top+t.height/2)}setDimensions(){this.setBoundingBox()}setBoundingBox(t){const{left:s,top:i,width:e,height:o,pathOffset:n,strokeOffset:r,strokeDiff:m}=this._calcDimensions();this.set({width:e,height:o,pathOffset:n,strokeOffset:r,strokeDiff:m}),t&&this.setPositionByOrigin(new h(s+e/2,i+o/2),w,w)}isStrokeAccountedForInDimensions(){return this.exactBoundingBox}_getNonTransformedDimensions(){return this.exactBoundingBox?new h(this.width,this.height):super._getNonTransformedDimensions()}_getTransformedDimensions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.exactBoundingBox){let n;if(Object.keys(t).some((t=>this.strokeUniform||this.constructor.layoutProperties.includes(t)))){var s,i;const{width:e,height:o}=this._calcDimensions(t);n=new h(null!==(s=t.width)&&void 0!==s?s:e,null!==(i=t.height)&&void 0!==i?i:o)}else{var e,o;n=new h(null!==(e=t.width)&&void 0!==e?e:this.width,null!==(o=t.height)&&void 0!==o?o:this.height)}return n.multiply(new h(t.scaleX||this.scaleX,t.scaleY||this.scaleY))}return super._getTransformedDimensions(t)}_set(t,s){const i=this.initialized&&this[t]!==s,e=super._set(t,s);return this.exactBoundingBox&&i&&((t===x||t===O)&&this.strokeUniform&&this.constructor.layoutProperties.includes("strokeUniform")||this.constructor.layoutProperties.includes(t))&&this.setDimensions(),e}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return s(s({},super.toObject(t)),{},{points:this.points.map((t=>{let{x:s,y:i}=t;return{x:s,y:i}}))})}_toSVG(){const t=[],s=this.pathOffset.x,i=this.pathOffset.y,o=e.NUM_FRACTION_DIGITS;for(let e=0,n=this.points.length;e<n;e++)t.push(u(this.points[e].x-s,o),",",u(this.points[e].y-i,o)," ");return["<".concat(this.constructor.type.toLowerCase()," "),"COMMON_PARTS",'points="'.concat(t.join(""),'" />\n')]}_render(t){const s=this.points.length,i=this.pathOffset.x,e=this.pathOffset.y;if(s&&!isNaN(this.points[s-1].y)){t.beginPath(),t.moveTo(this.points[0].x-i,this.points[0].y-e);for(let o=0;o<s;o++){const s=this.points[o];t.lineTo(s.x-i,s.y-e)}!this.isOpen()&&t.closePath(),this._renderPaintInOrder(t)}}complexity(){return this.points.length}static async fromElement(t,e,o){const h=r(t.getAttribute("points")),m=n(t,this.ATTRIBUTE_NAMES,o),a=i(m,b);return new this(h,s(s({},a),e))}static fromObject(t){return this._fromObject(t,{extraParam:"points"})}}t(D,"ownDefaults",_),t(D,"type","Polyline"),t(D,"layoutProperties",[j,y,"strokeLineCap","strokeLineJoin","strokeMiterLimit","strokeWidth","strokeUniform","points"]),t(D,"cacheProperties",[...B,"points"]),t(D,"ATTRIBUTE_NAMES",[...o]),m.setClass(D),m.setSVGClass(D);export{D as Polyline,_ as polylineDefaultValues};
//# sourceMappingURL=Polyline.min.mjs.map
