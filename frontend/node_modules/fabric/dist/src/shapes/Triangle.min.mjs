import{defineProperty as t,objectSpread2 as e}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{classRegistry as s}from"../ClassRegistry.min.mjs";import{FabricObject as i}from"./Object/FabricObject.min.mjs";const o={width:100,height:100};class n extends i{static getDefaults(){return e(e({},super.getDefaults()),n.ownDefaults)}constructor(t){super(),Object.assign(this,n.ownDefaults),this.setOptions(t)}_render(t){const e=this.width/2,s=this.height/2;t.beginPath(),t.moveTo(-e,s),t.lineTo(0,-s),t.lineTo(e,s),t.closePath(),this._renderPaintInOrder(t)}_toSVG(){const t=this.width/2,e=this.height/2;return["<polygon ","COMMON_PARTS",'points="',"".concat(-t," ").concat(e,",0 ").concat(-e,",").concat(t," ").concat(e),'" />']}}t(n,"type","Triangle"),t(n,"ownDefaults",o),s.setClass(n),s.setSVGClass(n);export{n as Triangle,o as triangleDefaultValues};
//# sourceMappingURL=Triangle.min.mjs.map
