import{defineProperty as e}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{BaseFilter as t}from"./BaseFilter.min.mjs";import{classRegistry as i}from"../ClassRegistry.min.mjs";import{fragmentSource as s}from"./shaders/pixelate.min.mjs";const o={blocksize:4};class r extends t{applyTo2d(e){let{imageData:{data:t,width:i,height:s}}=e;for(let e=0;e<s;e+=this.blocksize)for(let o=0;o<i;o+=this.blocksize){const r=4*e*i+4*o,l=t[r],a=t[r+1],m=t[r+2],n=t[r+3];for(let r=e;r<Math.min(e+this.blocksize,s);r++)for(let e=o;e<Math.min(o+this.blocksize,i);e++){const s=4*r*i+4*e;t[s]=l,t[s+1]=a,t[s+2]=m,t[s+3]=n}}}isNeutralState(){return 1===this.blocksize}getFragmentSource(){return s}sendUniformData(e,t){e.uniform1f(t.uBlocksize,this.blocksize)}}e(r,"type","Pixelate"),e(r,"defaults",o),e(r,"uniformLocations",["uBlocksize"]),i.setClass(r);export{r as Pixelate,o as pixelateDefaultValues};
//# sourceMappingURL=Pixelate.min.mjs.map
