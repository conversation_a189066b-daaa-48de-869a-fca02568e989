import{defineProperty as e}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{BaseFilter as r}from"./BaseFilter.min.mjs";import{classRegistry as t}from"../ClassRegistry.min.mjs";import{fragmentSource as a}from"./shaders/vibrance.min.mjs";const i={vibrance:0};class s extends r{getFragmentSource(){return a}applyTo2d(e){let{imageData:{data:r}}=e;const t=-this.vibrance;for(let e=0;e<r.length;e+=4){const a=r[e],i=r[e+1],s=r[e+2],n=Math.max(a,i,s),m=(a+i+s)/3,o=2*Math.abs(n-m)/255*t;r[e]+=n!==a?(n-a)*o:0,r[e+1]+=n!==i?(n-i)*o:0,r[e+2]+=n!==s?(n-s)*o:0}}sendUniformData(e,r){e.uniform1f(r.uVibrance,-this.vibrance)}isNeutralState(){return 0===this.vibrance}}e(s,"type","Vibrance"),e(s,"defaults",i),e(s,"uniformLocations",["uVibrance"]),t.setClass(s);export{s as Vibrance,i as vibranceDefaultValues};
//# sourceMappingURL=Vibrance.min.mjs.map
