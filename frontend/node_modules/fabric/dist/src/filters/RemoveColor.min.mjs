import{defineProperty as o}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{classRegistry as e}from"../ClassRegistry.min.mjs";import{Color as r}from"../color/Color.min.mjs";import{BaseFilter as t}from"./BaseFilter.min.mjs";import{fragmentShader as s}from"./shaders/removeColor.min.mjs";const i={color:"#FFFFFF",distance:.02,useAlpha:!1};class m extends t{getFragmentSource(){return s}applyTo2d(o){let{imageData:{data:e}}=o;const t=255*this.distance,s=new r(this.color).getSource(),i=[s[0]-t,s[1]-t,s[2]-t],m=[s[0]+t,s[1]+t,s[2]+t];for(let o=0;o<e.length;o+=4){const r=e[o],t=e[o+1],s=e[o+2];r>i[0]&&t>i[1]&&s>i[2]&&r<m[0]&&t<m[1]&&s<m[2]&&(e[o+3]=0)}}sendUniformData(o,e){const t=new r(this.color).getSource(),s=this.distance,i=[0+t[0]/255-s,0+t[1]/255-s,0+t[2]/255-s,1],m=[t[0]/255+s,t[1]/255+s,t[2]/255+s,1];o.uniform4fv(e.uLow,i),o.uniform4fv(e.uHigh,m)}}o(m,"type","RemoveColor"),o(m,"defaults",i),o(m,"uniformLocations",["uLow","uHigh"]),e.setClass(m);export{m as RemoveColor,i as removeColorDefaultValues};
//# sourceMappingURL=RemoveColor.min.mjs.map
