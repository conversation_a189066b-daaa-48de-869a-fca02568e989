import{log as e}from"../../util/internals/console.min.mjs";import{GLProbe as t}from"./GLProbe.min.mjs";class i extends t{testPrecision(e,t){const i="precision ".concat(t," float;\nvoid main(){}"),r=e.createShader(e.FRAGMENT_SHADER);return!!r&&(e.shaderSource(r,i),e.compileShader(r),!!e.getShaderParameter(r,e.COMPILE_STATUS))}queryWebGL(t){const i=t.getContext("webgl");i&&(this.maxTextureSize=i.getParameter(i.MAX_TEXTURE_SIZE),this.GLPrecision=["highp","mediump","lowp"].find((e=>this.testPrecision(i,e))),i.getExtension("WEBGL_lose_context").loseContext(),e("log","WebGL: max texture size ".concat(this.maxTextureSize)))}isSupported(e){return!!this.maxTextureSize&&this.maxTextureSize>=e}}export{i as WebGLProbe};
//# sourceMappingURL=WebGLProbe.min.mjs.map
