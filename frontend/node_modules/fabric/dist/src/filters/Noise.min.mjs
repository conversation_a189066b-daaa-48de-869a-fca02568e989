import{defineProperty as e}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{BaseFilter as s}from"./BaseFilter.min.mjs";import{classRegistry as t}from"../ClassRegistry.min.mjs";import{fragmentSource as o}from"./shaders/noise.min.mjs";const i={noise:0};class r extends s{getFragmentSource(){return o}applyTo2d(e){let{imageData:{data:s}}=e;const t=this.noise;for(let e=0;e<s.length;e+=4){const o=(.5-Math.random())*t;s[e]+=o,s[e+1]+=o,s[e+2]+=o}}sendUniformData(e,s){e.uniform1f(s.uNoise,this.noise/255),e.uniform1f(s.uSeed,Math.random())}isNeutralState(){return 0===this.noise}}e(r,"type","Noise"),e(r,"defaults",i),e(r,"uniformLocations",["uNoise","uSeed"]),t.setClass(r);export{r as Noise,i as noiseDefaultValues};
//# sourceMappingURL=Noise.min.mjs.map
