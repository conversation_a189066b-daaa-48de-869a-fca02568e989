import{defineProperty as t}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{BaseFilter as a}from"./BaseFilter.min.mjs";import{classRegistry as r}from"../ClassRegistry.min.mjs";import{fragmentSource as s}from"./shaders/saturation.min.mjs";const i={saturation:0};class o extends a{getFragmentSource(){return s}applyTo2d(t){let{imageData:{data:a}}=t;const r=-this.saturation;for(let t=0;t<a.length;t+=4){const s=a[t],i=a[t+1],o=a[t+2],e=Math.max(s,i,o);a[t]+=e!==s?(e-s)*r:0,a[t+1]+=e!==i?(e-i)*r:0,a[t+2]+=e!==o?(e-o)*r:0}}sendUniformData(t,a){t.uniform1f(a.uSaturation,-this.saturation)}isNeutralState(){return 0===this.saturation}}t(o,"type","Saturation"),t(o,"defaults",i),t(o,"uniformLocations",["uSaturation"]),r.setClass(o);export{o as Saturation,i as saturationDefaultValues};
//# sourceMappingURL=Saturation.min.mjs.map
