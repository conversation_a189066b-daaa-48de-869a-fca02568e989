import{getFabricWindow as e}from"../env/index.min.mjs";import{createCanvasElementFor as o,createCanvasElement as t}from"../util/misc/dom.min.mjs";import{WebGLFilterBackend as n}from"./WebGLFilterBackend.min.mjs";const r=e=>void 0!==e.webgl,i=(r,i)=>{const m=o({width:r,height:i}),a=t().getContext("webgl"),p={imageBuffer:new ArrayBuffer(r*i*4)},c={destinationWidth:r,destinationHeight:i,targetCanvas:m};let f;f=e().performance.now(),n.prototype.copyGLTo2D.call(p,a,c);const s=e().performance.now()-f;f=e().performance.now(),n.prototype.copyGLTo2DPutImageData.call(p,a,c);return s>e().performance.now()-f};export{i as isPutImageFaster,r as isWebGLPipelineState};
//# sourceMappingURL=utils.min.mjs.map
