import{defineProperty as e}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{config as t}from"../config.min.mjs";import{createCanvasElementFor as r}from"../util/misc/dom.min.mjs";class i{constructor(){let{tileSize:r=t.textureSize}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e(this,"aPosition",new Float32Array([0,0,0,1,1,0,1,1])),e(this,"resources",{}),this.tileSize=r,this.setupGLContext(r,r),this.captureGPUInfo()}setupGLContext(e,t){this.dispose(),this.createWebGLCanvas(e,t)}createWebGLCanvas(e,t){const i=r({width:e,height:t}),a=i.getContext("webgl",{alpha:!0,premultipliedAlpha:!1,depth:!1,stencil:!1,antialias:!1});a&&(a.clearColor(0,0,0,0),this.canvas=i,this.gl=a)}applyFilters(e,t,r,i,a,s){const n=this.gl,h=a.getContext("2d");if(!n||!h)return;let o;s&&(o=this.getCachedTexture(s,t));const u={originalWidth:t.width||t.naturalWidth||0,originalHeight:t.height||t.naturalHeight||0,sourceWidth:r,sourceHeight:i,destinationWidth:r,destinationHeight:i,context:n,sourceTexture:this.createTexture(n,r,i,o?void 0:t),targetTexture:this.createTexture(n,r,i),originalTexture:o||this.createTexture(n,r,i,o?void 0:t),passes:e.length,webgl:!0,aPosition:this.aPosition,programCache:this.programCache,pass:0,filterBackend:this,targetCanvas:a},c=n.createFramebuffer();return n.bindFramebuffer(n.FRAMEBUFFER,c),e.forEach((e=>{e&&e.applyTo(u)})),function(e){const t=e.targetCanvas,r=t.width,i=t.height,a=e.destinationWidth,s=e.destinationHeight;r===a&&i===s||(t.width=a,t.height=s)}(u),this.copyGLTo2D(n,u),n.bindTexture(n.TEXTURE_2D,null),n.deleteTexture(u.sourceTexture),n.deleteTexture(u.targetTexture),n.deleteFramebuffer(c),h.setTransform(1,0,0,1,0,0),u}dispose(){this.canvas&&(this.canvas=null,this.gl=null),this.clearWebGLCaches()}clearWebGLCaches(){this.programCache={},this.textureCache={}}createTexture(e,t,r,i,a){const{NEAREST:s,TEXTURE_2D:n,RGBA:h,UNSIGNED_BYTE:o,CLAMP_TO_EDGE:u,TEXTURE_MAG_FILTER:c,TEXTURE_MIN_FILTER:g,TEXTURE_WRAP_S:l,TEXTURE_WRAP_T:d}=e,T=e.createTexture();return e.bindTexture(n,T),e.texParameteri(n,c,a||s),e.texParameteri(n,g,a||s),e.texParameteri(n,l,u),e.texParameteri(n,d,u),i?e.texImage2D(n,0,h,h,o,i):e.texImage2D(n,0,h,t,r,0,h,o,null),T}getCachedTexture(e,t,r){const{textureCache:i}=this;if(i[e])return i[e];{const a=this.createTexture(this.gl,t.width,t.height,t,r);return a&&(i[e]=a),a}}evictCachesForKey(e){this.textureCache[e]&&(this.gl.deleteTexture(this.textureCache[e]),delete this.textureCache[e])}copyGLTo2D(e,t){const r=e.canvas,i=t.targetCanvas,a=i.getContext("2d");if(!a)return;a.translate(0,i.height),a.scale(1,-1);const s=r.height-i.height;a.drawImage(r,0,s,i.width,i.height,0,0,i.width,i.height)}copyGLTo2DPutImageData(e,t){const r=t.targetCanvas.getContext("2d"),i=t.destinationWidth,a=t.destinationHeight,s=i*a*4;if(!r)return;const n=new Uint8Array(this.imageBuffer,0,s),h=new Uint8ClampedArray(this.imageBuffer,0,s);e.readPixels(0,0,i,a,e.RGBA,e.UNSIGNED_BYTE,n);const o=new ImageData(h,i,a);r.putImageData(o,0,0)}captureGPUInfo(){if(this.gpuInfo)return this.gpuInfo;const e=this.gl,t={renderer:"",vendor:""};if(!e)return t;const r=e.getExtension("WEBGL_debug_renderer_info");if(r){const i=e.getParameter(r.UNMASKED_RENDERER_WEBGL),a=e.getParameter(r.UNMASKED_VENDOR_WEBGL);i&&(t.renderer=i.toLowerCase()),a&&(t.vendor=a.toLowerCase())}return this.gpuInfo=t,t}}export{i as WebGLFilterBackend};
//# sourceMappingURL=WebGLFilterBackend.min.mjs.map
