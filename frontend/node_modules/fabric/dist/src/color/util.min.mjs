const t=(t,n,a)=>(a<0&&(a+=1),a>1&&(a-=1),a<1/6?t+6*(n-t)*a:a<.5?n:a<2/3?t+(n-t)*(2/3-a)*6:t),n=(t,n,a,e)=>{t/=255,n/=255,a/=255;const r=Math.max(t,n,a),o=Math.min(t,n,a);let s,h;const c=(r+o)/2;if(r===o)s=h=0;else{const e=r-o;switch(h=c>.5?e/(2-r-o):e/(r+o),r){case t:s=(n-a)/e+(n<a?6:0);break;case n:s=(a-t)/e+2;break;case a:s=(t-n)/e+4}s/=6}return[Math.round(360*s),Math.round(100*h),Math.round(100*c),e]},a=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"1";return parseFloat(t)/(t.endsWith("%")?100:1)},e=t=>Math.min(Math.round(t),255).toString(16).toUpperCase().padStart(2,"0"),r=t=>{let[n,a,e,r=1]=t;const o=Math.round(.3*n+.59*a+.11*e);return[o,o,o,r]};export{a as fromAlphaToFloat,r as greyAverage,e as hexify,t as hue2rgb,n as rgb2Hsl};
//# sourceMappingURL=util.min.mjs.map
