import{objectSpread2 as e}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{applyViewboxTransform as t}from"./applyViewboxTransform.min.mjs";import{svgValidTagNamesRegEx as s}from"./constants.min.mjs";import{hasInvalidAncestor as n}from"./hasInvalidAncestor.min.mjs";import{parseUseDirectives as r}from"./parseUseDirectives.min.mjs";import{ElementsParser as o}from"./elements_parser.min.mjs";import{log as m,SignalAbortedError as i}from"../util/internals/console.min.mjs";import{getTagName as l}from"./getTagName.min.mjs";const a=e=>s.test(l(e)),p=()=>({objects:[],elements:[],options:{},allElements:[]});async function c(s,p){let{crossOrigin:c,signal:f}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(f&&f.aborted)return m("log",new i("parseSVGDocument")),{objects:[],elements:[],options:{},allElements:[]};const g=s.documentElement;r(s);const j=Array.from(g.getElementsByTagName("*")),u=e(e({},t(g)),{},{crossOrigin:c,signal:f}),b=j.filter((e=>(t(e),a(e)&&!n(e))));if(!b||b&&!b.length)return e(e({},{objects:[],elements:[],options:{},allElements:[]}),{},{options:u,allElements:j});const E={};j.filter((e=>"clipPath"===l(e))).forEach((e=>{e.setAttribute("originalTransform",e.getAttribute("transform")||"");const t=e.getAttribute("id");E[t]=Array.from(e.getElementsByTagName("*")).filter((e=>a(e)))}));const d=new o(b,u,p,s,E);return{objects:await d.parse(),elements:b,options:u,allElements:j}}export{p as createEmptyResponse,c as parseSVGDocument};
//# sourceMappingURL=parseSVGDocument.min.mjs.map
