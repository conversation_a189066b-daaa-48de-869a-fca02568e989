import{Color as t}from"../color/Color.min.mjs";import{toFixed as i}from"../util/misc/toFixed.min.mjs";import{FabricObject as r}from"../shapes/Object/FabricObject.min.mjs";const o={stroke:"strokeOpacity",fill:"fillOpacity"};function e(e){const n=r.getDefaults();return Object.entries(o).forEach((r=>{let[o,s]=r;if(void 0===e[s]||""===e[o])return;if(void 0===e[o]){if(!n[o])return;e[o]=n[o]}if(0===e[o].indexOf("url("))return;const c=new t(e[o]);e[o]=c.setAlpha(i(c.getAlpha()*e[s],2)).toRgba()})),e}export{e as setStrokeFillOpacity};
//# sourceMappingURL=setStrokeFillOpacity.min.mjs.map
