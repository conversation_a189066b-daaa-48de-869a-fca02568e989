import{taggedTemplateLiteral as t}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{iMatrix as a,ROTATE as r,SKEW_Y as e,SKEW_X as n,SCALE as s}from"../constants.min.mjs";import{reNum as c}from"./constants.min.mjs";import{cleanupSvgAttribute as i}from"../util/internals/cleanupSvgAttribute.min.mjs";import{createRotateMatrix as o,multiplyTransformMatrixArray as m,createSkewYMatrix as l,createSkewXMatrix as g,createScaleMatrix as p,createTranslateMatrix as w}from"../util/misc/matrix.min.mjs";var u,f,x,k,S,b,j;const h="(".concat(c,")"),v=String.raw(u||(u=t(["(skewX)(",")"],["(skewX)\\(","\\)"])),h),E=String.raw(f||(f=t(["(skewY)(",")"],["(skewY)\\(","\\)"])),h),R=String.raw(x||(x=t(["(rotate)(","(?: "," ",")?)"],["(rotate)\\(","(?: "," ",")?\\)"])),h,h,h),$=String.raw(k||(k=t(["(scale)(","(?: ",")?)"],["(scale)\\(","(?: ",")?\\)"])),h,h),A=String.raw(S||(S=t(["(translate)(","(?: ",")?)"],["(translate)\\(","(?: ",")?\\)"])),h,h),X=String.raw(b||(b=t(["(matrix)("," "," "," "," "," ",")"],["(matrix)\\("," "," "," "," "," ","\\)"])),h,h,h,h,h,h),Y="(?:".concat(X,"|").concat(A,"|").concat(R,"|").concat($,"|").concat(v,"|").concat(E,")"),_="(?:".concat(Y,"*)"),d=String.raw(j||(j=t(["^s*(?:","?)s*$"],["^\\s*(?:","?)\\s*$"])),_),y=new RegExp(d),B=new RegExp(Y),F=new RegExp(Y,"g");function H(t){const c=[];if(!(t=i(t).replace(/\s*([()])\s*/gi,"$1"))||t&&!y.test(t))return[...a];for(const i of t.matchAll(F)){const t=B.exec(i[0]);if(!t)continue;let m=a;const u=t.filter((t=>!!t)),[,f,...x]=u,[k,S,b,j,h,v]=x.map((t=>parseFloat(t)));switch(f){case"translate":m=w(k,S);break;case r:m=o({angle:k},{x:S,y:b});break;case s:m=p(k,S);break;case n:m=g(k);break;case e:m=l(k);break;case"matrix":m=[k,S,b,j,h,v]}c.push(m)}return m(c)}export{H as parseTransformAttribute};
//# sourceMappingURL=parseTransformAttribute.min.mjs.map
