import{objectSpread2 as t}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{DEFAULT_SVG_FONT_SIZE as o}from"../constants.min.mjs";import{parseUnit as r}from"../util/misc/svgParsing.min.mjs";import{svgValidParentsRegEx as m,cPath as e,fSize as n}from"./constants.min.mjs";import{getGlobalStylesForElement as i}from"./getGlobalStylesForElement.min.mjs";import{normalizeAttr as s}from"./normalizeAttr.min.mjs";import{normalizeValue as a}from"./normalizeValue.min.mjs";import{parseFontDeclaration as l}from"./parseFontDeclaration.min.mjs";import{parseStyleAttribute as p}from"./parseStyleAttribute.min.mjs";import{setStrokeFillOpacity as f}from"./setStrokeFillOpacity.min.mjs";function c(u,j,b){if(!u)return{};let d,S={},g=o;u.parentNode&&m.test(u.parentNode.nodeName)&&(S=c(u.parentElement,j,b),S.fontSize&&(d=g=r(S.fontSize)));const z=t(t(t({},j.reduce(((t,o)=>{const r=u.getAttribute(o);return r&&(t[o]=r),t}),{})),i(u,b)),p(u));z[e]&&u.setAttribute(e,z[e]),z[n]&&(d=r(z[n],g),z[n]="".concat(d));const A={};for(const t in z){const o=s(t),r=a(o,z[t],S,d);A[o]=r}A&&A.font&&l(A.font,A);const N=t(t({},S),A);return m.test(u.nodeName)?N:f(N)}export{c as parseAttributes};
//# sourceMappingURL=parseAttributes.min.mjs.map
