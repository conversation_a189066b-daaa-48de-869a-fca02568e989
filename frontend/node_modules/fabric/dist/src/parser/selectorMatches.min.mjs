function e(e,t){const n=e.nodeName,l=e.getAttribute("class"),g=e.getAttribute("id"),i="(?![a-zA-Z\\-]+)";let r;if(r=new RegExp("^"+n,"i"),t=t.replace(r,""),g&&t.length&&(r=new RegExp("#"+g+i,"i"),t=t.replace(r,"")),l&&t.length){const e=l.split(" ");for(let n=e.length;n--;)r=new RegExp("\\."+e[n]+i,"i"),t=t.replace(r,"")}return 0===t.length}export{e as selectorMatches};
//# sourceMappingURL=selectorMatches.min.mjs.map
