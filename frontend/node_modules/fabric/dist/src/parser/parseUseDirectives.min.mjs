import{svgNS as t}from"./constants.min.mjs";import{getMultipleNodes as e}from"./getMultipleNodes.min.mjs";import{applyViewboxTransform as n}from"./applyViewboxTransform.min.mjs";import{parseStyleString as o}from"./parseStyleString.min.mjs";function r(r){const s=e(r,["use","svg:use"]),i=["x","y","xlink:href","href","transform"];for(const e of s){const s=e.attributes,c={};for(const t of s)t.value&&(c[t.name]=t.value);const a=(c["xlink:href"]||c.href||"").slice(1);if(""===a)return;const f=r.getElementById(a);if(null===f)return;let m=f.cloneNode(!0);const l=m.attributes,u={};for(const t of l)t.value&&(u[t.name]=t.value);const{x:p=0,y:b=0,transform:d=""}=c,j="".concat(d," ").concat(u.transform||""," translate(").concat(p,", ").concat(b,")");if(n(m),/^svg$/i.test(m.nodeName)){const e=m.ownerDocument.createElementNS(t,"g");Object.entries(u).forEach((n=>{let[o,r]=n;return e.setAttributeNS(t,o,r)})),e.append(...m.childNodes),m=e}for(const t of s){if(!t)continue;const{name:e,value:n}=t;if(!i.includes(e))if("style"===e){const t={};o(n,t),Object.entries(u).forEach((e=>{let[n,o]=e;t[n]=o})),o(u.style||"",t);const r=Object.entries(t).map((t=>t.join(":"))).join(";");m.setAttribute(e,r)}else!u[e]&&m.setAttribute(e,n)}m.setAttribute("transform",j),m.setAttribute("instantiated_by_use","1"),m.removeAttribute("id"),e.parentNode.replaceChild(m,e)}}export{r as parseUseDirectives};
//# sourceMappingURL=parseUseDirectives.min.mjs.map
