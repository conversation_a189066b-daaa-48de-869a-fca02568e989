import{multiplyTransformMatrices as i}from"../util/misc/matrix.min.mjs";import{parseUnit as r}from"../util/misc/svgParsing.min.mjs";import{parseTransformAttribute as t}from"./parseTransformAttribute.min.mjs";import{FILL as e,STROKE as s,NONE as n,LEFT as o,RIGHT as a,CENTER as m}from"../constants.min.mjs";import{TEXT_DECORATION_THICKNESS as f}from"../shapes/Text/constants.min.mjs";function l(l,p,c,u){const x=Array.isArray(p);let h,d=p;if(l!==e&&l!==s||p!==n){if("strokeUniform"===l)return"non-scaling-stroke"===p;if("strokeDashArray"===l)d=p===n?null:p.replace(/,/g," ").split(/\s+/).map(parseFloat);else if("transformMatrix"===l)d=c&&c.transformMatrix?i(c.transformMatrix,t(p)):t(p);else if("visible"===l)d=p!==n&&"hidden"!==p,c&&!1===c.visible&&(d=!1);else if("opacity"===l)d=parseFloat(p),c&&void 0!==c.opacity&&(d*=c.opacity);else if("textAnchor"===l)d="start"===p?o:"end"===p?a:m;else if("charSpacing"===l||l===f)h=r(p,u)/u*1e3;else if("paintFirst"===l){const i=p.indexOf(e),r=p.indexOf(s);d=e,(i>-1&&r>-1&&r<i||-1===i&&r>-1)&&(d=s)}else{if("href"===l||"xlink:href"===l||"font"===l||"id"===l)return p;if("imageSmoothing"===l)return"optimizeQuality"===p;h=x?p.map(r):r(p,u)}}else d="";return!x&&isNaN(h)?d:h}export{l as normalizeValue};
//# sourceMappingURL=normalizeValue.min.mjs.map
