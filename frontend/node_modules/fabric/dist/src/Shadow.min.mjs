import{defineProperty as t}from"../_virtual/_rollupPluginBabelHelpers.min.mjs";import{classRegistry as o}from"./ClassRegistry.min.mjs";import{Color as e}from"./color/Color.min.mjs";import{config as s}from"./config.min.mjs";import{reNum as i}from"./parser/constants.min.mjs";import{Point as r}from"./Point.min.mjs";import{uid as n}from"./util/internals/uid.min.mjs";import{pickBy as f}from"./util/misc/pick.min.mjs";import{degreesToRadians as c}from"./util/misc/radiansDegreesConversion.min.mjs";import{toFixed as a}from"./util/misc/toFixed.min.mjs";import{rotateVector as l}from"./util/misc/vectors.min.mjs";const m="(-?\\d+(?:\\.\\d*)?(?:px)?(?:\\s?|$))?",u=new RegExp("(?:\\s|^)"+m+m+"("+i+"?(?:px)?)?(?:\\s?|$)(?:$|\\s)"),h={color:"rgb(0,0,0)",blur:0,offsetX:0,offsetY:0,affectStroke:!1,includeDefaultValues:!0,nonScaling:!1};class p{constructor(t){const o="string"==typeof t?p.parseShadow(t):t;Object.assign(this,p.ownDefaults,o),this.id=n()}static parseShadow(t){const o=t.trim(),[,e=0,s=0,i=0]=(u.exec(o)||[]).map((t=>parseFloat(t)||0));return{color:(o.replace(u,"")||"rgb(0,0,0)").trim(),offsetX:e,offsetY:s,blur:i}}toString(){return[this.offsetX,this.offsetY,this.blur,this.color].join("px ")}toSVG(t){const o=l(new r(this.offsetX,this.offsetY),c(-t.angle)),i=new e(this.color);let n=40,f=40;return t.width&&t.height&&(n=100*a((Math.abs(o.x)+this.blur)/t.width,s.NUM_FRACTION_DIGITS)+20,f=100*a((Math.abs(o.y)+this.blur)/t.height,s.NUM_FRACTION_DIGITS)+20),t.flipX&&(o.x*=-1),t.flipY&&(o.y*=-1),'<filter id="SVGID_'.concat(this.id,'" y="-').concat(f,'%" height="').concat(100+2*f,'%" x="-').concat(n,'%" width="').concat(100+2*n,'%" >\n\t<feGaussianBlur in="SourceAlpha" stdDeviation="').concat(a(this.blur?this.blur/2:0,s.NUM_FRACTION_DIGITS),'"></feGaussianBlur>\n\t<feOffset dx="').concat(a(o.x,s.NUM_FRACTION_DIGITS),'" dy="').concat(a(o.y,s.NUM_FRACTION_DIGITS),'" result="oBlur" ></feOffset>\n\t<feFlood flood-color="').concat(i.toRgb(),'" flood-opacity="').concat(i.getAlpha(),'"/>\n\t<feComposite in2="oBlur" operator="in" />\n\t<feMerge>\n\t\t<feMergeNode></feMergeNode>\n\t\t<feMergeNode in="SourceGraphic"></feMergeNode>\n\t</feMerge>\n</filter>\n')}toObject(){const t={color:this.color,blur:this.blur,offsetX:this.offsetX,offsetY:this.offsetY,affectStroke:this.affectStroke,nonScaling:this.nonScaling,type:this.constructor.type},o=p.ownDefaults;return this.includeDefaultValues?t:f(t,((t,e)=>t!==o[e]))}static async fromObject(t){return new this(t)}}t(p,"ownDefaults",h),t(p,"type","shadow"),o.setClass(p,"shadow");export{p as Shadow,h as shadowDefaultValues};
//# sourceMappingURL=Shadow.min.mjs.map
