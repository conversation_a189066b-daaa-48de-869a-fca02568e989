import{cos as t}from"./util/misc/cos.min.mjs";import{sin as s}from"./util/misc/sin.min.mjs";class i{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;"object"==typeof t?(this.x=t.x,this.y=t.y):(this.x=t,this.y=s)}add(t){return new i(this.x+t.x,this.y+t.y)}addEquals(t){return this.x+=t.x,this.y+=t.y,this}scalarAdd(t){return new i(this.x+t,this.y+t)}scalarAddEquals(t){return this.x+=t,this.y+=t,this}subtract(t){return new i(this.x-t.x,this.y-t.y)}subtractEquals(t){return this.x-=t.x,this.y-=t.y,this}scalarSubtract(t){return new i(this.x-t,this.y-t)}scalarSubtractEquals(t){return this.x-=t,this.y-=t,this}multiply(t){return new i(this.x*t.x,this.y*t.y)}scalarMultiply(t){return new i(this.x*t,this.y*t)}scalarMultiplyEquals(t){return this.x*=t,this.y*=t,this}divide(t){return new i(this.x/t.x,this.y/t.y)}scalarDivide(t){return new i(this.x/t,this.y/t)}scalarDivideEquals(t){return this.x/=t,this.y/=t,this}eq(t){return this.x===t.x&&this.y===t.y}lt(t){return this.x<t.x&&this.y<t.y}lte(t){return this.x<=t.x&&this.y<=t.y}gt(t){return this.x>t.x&&this.y>t.y}gte(t){return this.x>=t.x&&this.y>=t.y}lerp(t){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5;return s=Math.max(Math.min(1,s),0),new i(this.x+(t.x-this.x)*s,this.y+(t.y-this.y)*s)}distanceFrom(t){const s=this.x-t.x,i=this.y-t.y;return Math.sqrt(s*s+i*i)}midPointFrom(t){return this.lerp(t)}min(t){return new i(Math.min(this.x,t.x),Math.min(this.y,t.y))}max(t){return new i(Math.max(this.x,t.x),Math.max(this.y,t.y))}toString(){return"".concat(this.x,",").concat(this.y)}setXY(t,s){return this.x=t,this.y=s,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setFromPoint(t){return this.x=t.x,this.y=t.y,this}swap(t){const s=this.x,i=this.y;this.x=t.x,this.y=t.y,t.x=s,t.y=i}clone(){return new i(this.x,this.y)}rotate(r){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:h;const n=s(r),x=t(r),y=this.subtract(e);return new i(y.x*x-y.y*n,y.x*n+y.y*x).add(e)}transform(t){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new i(t[0]*this.x+t[2]*this.y+(s?0:t[4]),t[1]*this.x+t[3]*this.y+(s?0:t[5]))}}const h=new i(0,0);export{i as Point,h as ZERO};
//# sourceMappingURL=Point.min.mjs.map
