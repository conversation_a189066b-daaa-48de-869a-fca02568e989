import{Pattern as t}from"../Pattern/Pattern.min.mjs";import{createCanvasElement as e}from"../util/misc/dom.min.mjs";import{PencilBrush as r}from"./PencilBrush.min.mjs";class s extends r{constructor(t){super(t)}getPatternSrc(){const t=e(),r=t.getContext("2d");return t.width=t.height=25,r&&(r.fillStyle=this.color,r.beginPath(),r.arc(10,10,10,0,2*Math.PI,!1),r.closePath(),r.fill()),t}getPattern(t){return t.createPattern(this.source||this.getPatternSrc(),"repeat")}_setBrushStyles(t){super._setBrushStyles(t);const e=this.getPattern(t);e&&(t.strokeStyle=e)}createPath(e){const r=super.createPath(e),s=r._getLeftTopCoords().scalarAdd(r.strokeWidth/2);return r.stroke=new t({source:this.source||this.getPatternSrc(),offsetX:-s.x,offsetY:-s.y}),r}}export{s as PatternBrush};
//# sourceMappingURL=PatternBrush.min.mjs.map
