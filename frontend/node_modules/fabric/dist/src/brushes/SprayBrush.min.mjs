import{defineProperty as t}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{Group as s}from"../shapes/Group.min.mjs";import{Shadow as i}from"../Shadow.min.mjs";import{Rect as h}from"../shapes/Rect.min.mjs";import{getRandomInt as n}from"../util/internals/getRandomInt.min.mjs";import{BaseBrush as e}from"./BaseBrush.min.mjs";import{CENTER as a}from"../constants.min.mjs";class o extends e{constructor(s){super(s),t(this,"width",10),t(this,"density",20),t(this,"dotWidth",1),t(this,"dotWidthVariance",1),t(this,"randomOpacity",!1),t(this,"optimizeOverlapping",!0),this.sprayChunks=[],this.sprayChunk=[]}onMouseDown(t){this.sprayChunks=[],this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.addSprayChunk(t),this.renderChunck(this.sprayChunk)}onMouseMove(t){!0===this.limitedToCanvasSize&&this._isOutSideCanvas(t)||(this.addSprayChunk(t),this.renderChunck(this.sprayChunk))}onMouseUp(){const t=this.canvas.renderOnAddRemove;this.canvas.renderOnAddRemove=!1;const n=[];for(let t=0;t<this.sprayChunks.length;t++){const s=this.sprayChunks[t];for(let t=0;t<s.length;t++){const i=s[t],e=new h({width:i.width,height:i.width,left:i.x+1,top:i.y+1,originX:a,originY:a,fill:this.color});n.push(e)}}const e=new s(this.optimizeOverlapping?function(t){const s={},i=[];for(let h,n=0;n<t.length;n++)h="".concat(t[n].left).concat(t[n].top),s[h]||(s[h]=!0,i.push(t[n]));return i}(n):n,{objectCaching:!0,subTargetCheck:!1,interactive:!1});this.shadow&&e.set("shadow",new i(this.shadow)),this.canvas.fire("before:path:created",{path:e}),this.canvas.add(e),this.canvas.fire("path:created",{path:e}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=t,this.canvas.requestRenderAll()}renderChunck(t){const s=this.canvas.contextTop;s.fillStyle=this.color,this._saveAndTransform(s);for(let i=0;i<t.length;i++){const h=t[i];s.globalAlpha=h.opacity,s.fillRect(h.x,h.y,h.width,h.width)}s.restore()}_render(){const t=this.canvas.contextTop;t.fillStyle=this.color,this._saveAndTransform(t);for(let t=0;t<this.sprayChunks.length;t++)this.renderChunck(this.sprayChunks[t]);t.restore()}addSprayChunk(t){this.sprayChunk=[];const s=this.width/2;for(let i=0;i<this.density;i++)this.sprayChunk.push({x:n(t.x-s,t.x+s),y:n(t.y-s,t.y+s),width:this.dotWidthVariance?n(Math.max(1,this.dotWidth-this.dotWidthVariance),this.dotWidth+this.dotWidthVariance):this.dotWidth,opacity:this.randomOpacity?n(0,100)/100:1});this.sprayChunks.push(this.sprayChunk)}}export{o as SprayBrush};
//# sourceMappingURL=SprayBrush.min.mjs.map
