import{defineProperty as t}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{Shadow as s}from"../Shadow.min.mjs";import{Path as i}from"../shapes/Path.min.mjs";import{getSmoothPathFromPoints as e,joinPath as n}from"../util/path/index.min.mjs";import{BaseBrush as h}from"./BaseBrush.min.mjs";class o extends h{constructor(s){super(s),t(this,"decimate",.4),t(this,"drawStraightLine",!1),t(this,"straightLineKey","shiftKey"),this._points=[],this._hasStraightLine=!1}needsFullRender(){return super.needsFullRender()||this._hasStraightLine}static drawSegment(t,s,i){const e=s.midPointFrom(i);return t.quadraticCurveTo(s.x,s.y,e.x,e.y),e}onMouseDown(t,s){let{e:i}=s;this.canvas._isMainEvent(i)&&(this.drawStraightLine=!!this.straightLineKey&&i[this.straightLineKey],this._prepareForDrawing(t),this._addPoint(t),this._render())}onMouseMove(t,s){let{e:i}=s;if(this.canvas._isMainEvent(i)&&(this.drawStraightLine=!!this.straightLineKey&&i[this.straightLineKey],(!0!==this.limitedToCanvasSize||!this._isOutSideCanvas(t))&&this._addPoint(t)&&this._points.length>1))if(this.needsFullRender())this.canvas.clearContext(this.canvas.contextTop),this._render();else{const t=this._points,s=t.length,i=this.canvas.contextTop;this._saveAndTransform(i),this.oldEnd&&(i.beginPath(),i.moveTo(this.oldEnd.x,this.oldEnd.y)),this.oldEnd=o.drawSegment(i,t[s-2],t[s-1]),i.stroke(),i.restore()}}onMouseUp(t){let{e:s}=t;return!this.canvas._isMainEvent(s)||(this.drawStraightLine=!1,this.oldEnd=void 0,this._finalizeAndAddPath(),!1)}_prepareForDrawing(t){this._reset(),this._addPoint(t),this.canvas.contextTop.moveTo(t.x,t.y)}_addPoint(t){return!(this._points.length>1&&t.eq(this._points[this._points.length-1]))&&(this.drawStraightLine&&this._points.length>1&&(this._hasStraightLine=!0,this._points.pop()),this._points.push(t),!0)}_reset(){this._points=[],this._setBrushStyles(this.canvas.contextTop),this._setShadow(),this._hasStraightLine=!1}_render(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.canvas.contextTop,s=this._points[0],i=this._points[1];if(this._saveAndTransform(t),t.beginPath(),2===this._points.length&&s.x===i.x&&s.y===i.y){const t=this.width/1e3;s.x-=t,i.x+=t}t.moveTo(s.x,s.y);for(let e=1;e<this._points.length;e++)o.drawSegment(t,s,i),s=this._points[e],i=this._points[e+1];t.lineTo(s.x,s.y),t.stroke(),t.restore()}convertPointsToSVGPath(t){const s=this.width/1e3;return e(t,s)}createPath(t){const e=new i(t,{fill:null,stroke:this.color,strokeWidth:this.width,strokeLineCap:this.strokeLineCap,strokeMiterLimit:this.strokeMiterLimit,strokeLineJoin:this.strokeLineJoin,strokeDashArray:this.strokeDashArray});return this.shadow&&(this.shadow.affectStroke=!0,e.shadow=new s(this.shadow)),e}decimatePoints(t,s){if(t.length<=2)return t;let i,e=t[0];const n=this.canvas.getZoom(),h=Math.pow(s/n,2),o=t.length-1,a=[e];for(let s=1;s<o-1;s++)i=Math.pow(e.x-t[s].x,2)+Math.pow(e.y-t[s].y,2),i>=h&&(e=t[s],a.push(e));return a.push(t[o]),a}_finalizeAndAddPath(){this.canvas.contextTop.closePath(),this.decimate&&(this._points=this.decimatePoints(this._points,this.decimate));const t=this.convertPointsToSVGPath(this._points);if(function(t){return"M 0 0 Q 0 0 0 0 L 0 0"===n(t)}(t))return void this.canvas.requestRenderAll();const s=this.createPath(t);this.canvas.clearContext(this.canvas.contextTop),this.canvas.fire("before:path:created",{path:s}),this.canvas.add(s),this.canvas.requestRenderAll(),s.setCoords(),this._resetShadow(),this.canvas.fire("path:created",{path:s})}}export{o as PencilBrush};
//# sourceMappingURL=PencilBrush.min.mjs.map
