import{defineProperty as t,objectSpread2 as e,objectWithoutProperties as s}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{config as r}from"../config.min.mjs";import{ifNaN as i}from"../util/internals/ifNaN.min.mjs";import{uid as o}from"../util/internals/uid.min.mjs";import{loadImage as n}from"../util/misc/objectEnlive.min.mjs";import{pick as a}from"../util/misc/pick.min.mjs";import{toFixed as c}from"../util/misc/toFixed.min.mjs";import{classRegistry as h}from"../ClassRegistry.min.mjs";import{log as m}from"../util/internals/console.min.mjs";const u=["type","source","patternTransform"];class p{get type(){return"pattern"}set type(t){m("warn","Setting type has no effect",t)}constructor(e){t(this,"repeat","repeat"),t(this,"offsetX",0),t(this,"offsetY",0),t(this,"crossOrigin",""),this.id=o(),Object.assign(this,e)}isImageSource(){return!!this.source&&"string"==typeof this.source.src}isCanvasSource(){return!!this.source&&!!this.source.toDataURL}sourceToString(){return this.isImageSource()?this.source.src:this.isCanvasSource()?this.source.toDataURL():""}toLive(t){return this.source&&(!this.isImageSource()||this.source.complete&&0!==this.source.naturalWidth&&0!==this.source.naturalHeight)?t.createPattern(this.source,this.repeat):null}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];const{repeat:s,crossOrigin:i}=this;return e(e({},a(this,t)),{},{type:"pattern",source:this.sourceToString(),repeat:s,crossOrigin:i,offsetX:c(this.offsetX,r.NUM_FRACTION_DIGITS),offsetY:c(this.offsetY,r.NUM_FRACTION_DIGITS),patternTransform:this.patternTransform?[...this.patternTransform]:null})}toSVG(t){let{width:e,height:s}=t;const{source:r,repeat:o,id:n}=this,a=i(this.offsetX/e,0),c=i(this.offsetY/s,0),h="repeat-y"===o||"no-repeat"===o?1+Math.abs(a||0):i(r.width/e,0),m="repeat-x"===o||"no-repeat"===o?1+Math.abs(c||0):i(r.height/s,0);return['<pattern id="SVGID_'.concat(n,'" x="').concat(a,'" y="').concat(c,'" width="').concat(h,'" height="').concat(m,'">'),'<image x="0" y="0" width="'.concat(r.width,'" height="').concat(r.height,'" xlink:href="').concat(this.sourceToString(),'"></image>'),"</pattern>",""].join("\n")}static async fromObject(t,r){let{type:i,source:o,patternTransform:a}=t,c=s(t,u);const h=await n(o,e(e({},r),{},{crossOrigin:c.crossOrigin}));return new this(e(e({},c),{},{patternTransform:a&&a.slice(0),source:h}))}}t(p,"type","Pattern"),h.setClass(p),h.setClass(p,"pattern");export{p as Pattern};
//# sourceMappingURL=Pattern.min.mjs.map
