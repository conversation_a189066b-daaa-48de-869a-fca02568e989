import{objectSpread2 as r}from"../../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{isPercent as t}from"../../parser/percent.min.mjs";import{parseGradientUnits as n,parseType as e}from"./misc.min.mjs";function i(r,t){return r.getAttribute(t)}function o(r){return{x1:i(r,"x1")||0,y1:i(r,"y1")||0,x2:i(r,"x2")||"100%",y2:i(r,"y2")||0}}function u(r){return{x1:i(r,"fx")||i(r,"cx")||"50%",y1:i(r,"fy")||i(r,"cy")||"50%",r1:0,x2:i(r,"cx")||"50%",y2:i(r,"cy")||"50%",r2:i(r,"r")||"50%"}}function y(i,y){return function(r,n){let e,{width:i,height:o,gradientUnits:u}=n;return Object.keys(r).reduce(((n,y)=>{const c=r[y];return"Infinity"===c?e=1:"-Infinity"===c?e=0:(e="string"==typeof c?parseFloat(c):c,"string"==typeof c&&t(c)&&(e*=.01,"pixels"===u&&("x1"!==y&&"x2"!==y&&"r2"!==y||(e*=i),"y1"!==y&&"y2"!==y||(e*=o)))),n[y]=e,n}),{})}("linear"===e(i)?o(i):u(i),r(r({},y),{},{gradientUnits:n(i)}))}export{y as parseCoords,o as parseLinearCoords,u as parseRadialCoords};
//# sourceMappingURL=parseCoords.min.mjs.map
