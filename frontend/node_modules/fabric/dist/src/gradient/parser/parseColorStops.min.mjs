import{Color as t}from"../../color/Color.min.mjs";import{parsePercent as o}from"../../parser/percent.min.mjs";import{ifNaN as e}from"../../util/internals/ifNaN.min.mjs";const r=/\s*;\s*/,s=/\s*:\s*/;function n(n,i){let p,l;const c=n.getAttribute("style");if(c){const t=c.split(r);""===t[t.length-1]&&t.pop();for(let o=t.length;o--;){const[e,r]=t[o].split(s).map((t=>t.trim()));"stop-color"===e?p=r:"stop-opacity"===e&&(l=r)}}const m=new t(p||n.getAttribute("stop-color")||"rgb(0,0,0)");return{offset:o(n.getAttribute("offset"),0),color:m.toRgb(),opacity:e(parseFloat(l||n.getAttribute("stop-opacity")||""),1)*m.getAlpha()*i}}function i(t,e){const r=[],s=t.getElementsByTagName("stop"),i=o(e,1);for(let t=s.length;t--;)r.push(n(s[t],i));return r}export{i as parseColorStops};
//# sourceMappingURL=parseColorStops.min.mjs.map
