import{CENTER as t,SCALE_X as r,SCALE_Y as e}from"../constants.min.mjs";import{qrDecompose as s}from"./misc/matrix.min.mjs";const o=(o,i)=>{let a=o._findCenterFromElement();o.transformMatrix&&((t=>{if(t.transformMatrix){const{scaleX:o,scaleY:i,angle:a,skewX:n}=s(t.transformMatrix);t.flipX=!1,t.flipY=!1,t.set(r,o),t.set(e,i),t.angle=a,t.skewX=n,t.skewY=0}})(o),a=a.transform(o.transformMatrix)),delete o.transformMatrix,i&&(o.scaleX*=i.scaleX,o.scaleY*=i.scaleY,o.cropX=i.cropX,o.cropY=i.cropY,a.x+=i.offsetLeft,a.y+=i.offsetTop,o.width=i.width,o.height=i.height),o.setPositionByOrigin(a,t,t)};export{o as removeTransformMatrixForSvgParsing};
//# sourceMappingURL=transform_matrix_removal.min.mjs.map
