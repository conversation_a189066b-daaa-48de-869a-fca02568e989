import{defineProperty as t}from"../../../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{Point as i}from"../../../Point.min.mjs";import{halfPI as s,twoMathPi as o}from"../../../constants.min.mjs";import{degreesToRadians as e}from"../radiansDegreesConversion.min.mjs";import{calcAngleBetweenVectors as r,calcVectorRotation as h,getUnitVector as a,rotateVector as n,getOrthonormalVector as c,magnitude as l,isBetweenVectors as p,crossProduct as u}from"../vectors.min.mjs";import{StrokeProjectionsBase as m}from"./StrokeProjectionsBase.min.mjs";const d=new i;class j extends m{static getOrthogonalRotationFactor(t,i){const o=i?r(t,i):h(t);return Math.abs(o)<s?-1:1}constructor(s,o,e,h){super(h),t(this,"AB",void 0),t(this,"AC",void 0),t(this,"alpha",void 0),t(this,"bisector",void 0),this.A=new i(s),this.B=new i(o),this.C=new i(e),this.AB=this.createSideVector(this.A,this.B),this.AC=this.createSideVector(this.A,this.C),this.alpha=r(this.AB,this.AC),this.bisector=a(n(this.AB.eq(d)?this.AC:this.AB,this.alpha/2))}calcOrthogonalProjection(t,i){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.strokeProjectionMagnitude;const o=this.createSideVector(t,i),e=c(o),r=j.getOrthogonalRotationFactor(e,this.bisector);return this.scaleUnitVector(e,s*r)}projectBevel(){const t=[];return(this.alpha%o==0?[this.B]:[this.B,this.C]).forEach((i=>{t.push(this.projectOrthogonally(this.A,i)),t.push(this.projectOrthogonally(this.A,i,-this.strokeProjectionMagnitude))})),t}projectMiter(){const t=[],i=Math.abs(this.alpha),s=1/Math.sin(i/2),o=this.scaleUnitVector(this.bisector,-this.strokeProjectionMagnitude*s),e=this.options.strokeUniform?l(this.scaleUnitVector(this.bisector,this.options.strokeMiterLimit)):this.options.strokeMiterLimit;return l(o)/this.strokeProjectionMagnitude<=e&&t.push(this.applySkew(this.A.add(o))),t.push(...this.projectBevel()),t}projectRoundNoSkew(t,s){const o=[],e=new i(j.getOrthogonalRotationFactor(this.bisector),j.getOrthogonalRotationFactor(new i(this.bisector.y,this.bisector.x)));return[new i(1,0).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar).multiply(e),new i(0,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar).multiply(e)].forEach((i=>{p(i,t,s)&&o.push(this.A.add(i))})),o}projectRoundWithSkew(t,s){const o=[],{skewX:r,skewY:h,scaleX:a,scaleY:n,strokeUniform:c}=this.options,l=new i(Math.tan(e(r)),Math.tan(e(h))),u=this.strokeProjectionMagnitude,m=c?u/n/Math.sqrt(1/n**2+1/a**2*l.y**2):u/Math.sqrt(1+l.y**2),d=new i(Math.sqrt(Math.max(u**2-m**2,0)),m),j=c?u/Math.sqrt(1+l.x**2*(1/n)**2/(1/a+1/a*l.x*l.y)**2):u/Math.sqrt(1+l.x**2/(1+l.x*l.y)**2),k=new i(j,Math.sqrt(Math.max(u**2-j**2,0)));return[k,k.scalarMultiply(-1),d,d.scalarMultiply(-1)].map((t=>this.applySkew(c?t.multiply(this.strokeUniformScalar):t))).forEach((i=>{p(i,t,s)&&o.push(this.applySkew(this.A).add(i))})),o}projectRound(){const t=[];t.push(...this.projectBevel());const i=this.alpha%o==0,s=this.applySkew(this.A),e=t[i?0:2].subtract(s),r=t[i?1:0].subtract(s),h=i?this.applySkew(this.AB.scalarMultiply(-1)):this.applySkew(this.bisector.multiply(this.strokeUniformScalar).scalarMultiply(-1)),a=u(e,h)>0,n=a?e:r,c=a?r:e;return this.isSkewed()?t.push(...this.projectRoundWithSkew(n,c)):t.push(...this.projectRoundNoSkew(n,c)),t}projectPoints(){switch(this.options.strokeLineJoin){case"miter":return this.projectMiter();case"round":return this.projectRound();default:return this.projectBevel()}}project(){return this.projectPoints().map((t=>({originPoint:this.A,projectedPoint:t,angle:this.alpha,bisector:this.bisector})))}}export{j as StrokeLineJoinProjections};
//# sourceMappingURL=StrokeLineJoinProjections.min.mjs.map
