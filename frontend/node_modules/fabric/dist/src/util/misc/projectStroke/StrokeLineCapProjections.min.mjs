import{Point as t}from"../../../Point.min.mjs";import{getOrthonormalVector as i,getUnitVector as s}from"../vectors.min.mjs";import{StrokeLineJoinProjections as e}from"./StrokeLineJoinProjections.min.mjs";import{StrokeProjectionsBase as o}from"./StrokeProjectionsBase.min.mjs";class r extends o{constructor(i,s,e){super(e),this.A=new t(i),this.T=new t(s)}calcOrthogonalProjection(t,s){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.strokeProjectionMagnitude;const o=this.createSideVector(t,s);return this.scaleUnitVector(i(o),e)}projectButt(){return[this.projectOrthogonally(this.A,this.T,this.strokeProjectionMagnitude),this.projectOrthogonally(this.A,this.T,-this.strokeProjectionMagnitude)]}projectRound(){const i=[];if(!this.isSkewed()&&this.A.eq(this.T)){const s=new t(1,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar);i.push(this.applySkew(this.A.add(s)),this.applySkew(this.A.subtract(s)))}else i.push(...new e(this.A,this.T,this.T,this.options).projectRound());return i}projectSquare(){const i=[];if(this.A.eq(this.T)){const s=new t(1,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar);i.push(this.A.add(s),this.A.subtract(s))}else{const t=this.calcOrthogonalProjection(this.A,this.T,this.strokeProjectionMagnitude),e=this.scaleUnitVector(s(this.createSideVector(this.A,this.T)),-this.strokeProjectionMagnitude),o=this.A.add(e);i.push(o.add(t),o.subtract(t))}return i.map((t=>this.applySkew(t)))}projectPoints(){switch(this.options.strokeLineCap){case"round":return this.projectRound();case"square":return this.projectSquare();default:return this.projectButt()}}project(){return this.projectPoints().map((t=>({originPoint:this.A,projectedPoint:t})))}}export{r as StrokeLineCapProjections};
//# sourceMappingURL=StrokeLineCapProjections.min.mjs.map
