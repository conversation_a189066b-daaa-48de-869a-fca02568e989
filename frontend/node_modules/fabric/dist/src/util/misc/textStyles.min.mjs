import{objectSpread2 as t}from"../../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{reNewline as e}from"../../constants.min.mjs";import{cloneStyles as n}from"../internals/cloneStyles.min.mjs";import{graphemeSplit as o}from"../lang_string.min.mjs";const r=function(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t.fill!==e.fill||t.stroke!==e.stroke||t.strokeWidth!==e.strokeWidth||t.fontSize!==e.fontSize||t.fontFamily!==e.fontFamily||t.fontWeight!==e.fontWeight||t.fontStyle!==e.fontStyle||t.textDecorationThickness!==e.textDecorationThickness||t.textBackgroundColor!==e.textBackgroundColor||t.deltaY!==e.deltaY||n&&(t.overline!==e.overline||t.underline!==e.underline||t.linethrough!==e.linethrough)},l=(t,e)=>{const l=e.split("\n"),i=[];let s=-1,f={};t=n(t);for(let e=0;e<l.length;e++){const n=o(l[e]);if(t[e])for(let o=0;o<n.length;o++){s++;const n=t[e][o];n&&Object.keys(n).length>0&&(r(f,n,!0)?i.push({start:s,end:s+1,style:n}):i[i.length-1].end++),f=n||{}}else s+=n.length,f={}}return i},i=(r,l)=>{if(!Array.isArray(r))return n(r);const i=l.split(e),s={};let f=-1,a=0;for(let e=0;e<i.length;e++){const n=o(i[e]);for(let o=0;o<n.length;o++)f++,r[a]&&r[a].start<=f&&f<r[a].end&&(s[e]=s[e]||{},s[e][o]=t({},r[a].style),f===r[a].end-1&&a++)}return s};export{r as hasStyleChanged,i as stylesFromArray,l as stylesToArray};
//# sourceMappingURL=textStyles.min.mjs.map
