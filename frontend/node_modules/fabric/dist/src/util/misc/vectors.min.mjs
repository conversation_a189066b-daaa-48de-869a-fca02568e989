import{Point as t}from"../../Point.min.mjs";const e=new t(1,0),n=new t,r=(t,e)=>t.rotate(e),a=(e,n)=>new t(n).subtract(e),i=t=>t.distanceFrom(n),o=(t,e)=>Math.atan2(u(t,e),x(t,e)),c=t=>o(e,t),s=t=>t.eq(n)?t:t.scalarDivide(i(t)),l=function(e){let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return s(new t(-e.y,e.x).scalarMultiply(n?1:-1))},u=(t,e)=>t.x*e.y-t.y*e.x,x=(t,e)=>t.x*e.x+t.y*e.y,y=(t,e,n)=>{if(t.eq(e)||t.eq(n))return!0;const r=u(e,n),a=u(e,t),i=u(n,t);return r>=0?a>=0&&i<=0:!(a<=0&&i>=0)};export{o as calcAngleBetweenVectors,c as calcVectorRotation,a as createVector,u as crossProduct,x as dotProduct,l as getOrthonormalVector,s as getUnitVector,y as isBetweenVectors,i as magnitude,r as rotateVector};
//# sourceMappingURL=vectors.min.mjs.map
