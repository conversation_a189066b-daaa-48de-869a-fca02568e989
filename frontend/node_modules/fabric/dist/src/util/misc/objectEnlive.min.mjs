import{noop as e}from"../../constants.min.mjs";import{createImage as n}from"./dom.min.mjs";import{classRegistry as t}from"../../ClassRegistry.min.mjs";import{SignalAbortedError as o,FabricError as r}from"../internals/console.min.mjs";const s=function(e){let{signal:t,crossOrigin:s=null}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(i,a){if(t&&t.aborted)return a(new o("loadImage"));const l=n();let c;t&&(c=function(e){l.src="",a(e)},t.addEventListener("abort",c,{once:!0}));const m=function(){l.onload=l.onerror=null,c&&(null==t||t.removeEventListener("abort",c)),i(l)};e?(l.onload=m,l.onerror=function(){c&&(null==t||t.removeEventListener("abort",c)),a(new r("Error loading ".concat(l.src)))},s&&(l.crossOrigin=s),l.src=e):m()}))},i=function(n){let{signal:o,reviver:r=e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise(((e,s)=>{const i=[];o&&o.addEventListener("abort",s,{once:!0}),Promise.all(n.map((e=>t.getClass(e.type).fromObject(e,{signal:o}).then((n=>(r(e,n),i.push(n),n)))))).then(e).catch((e=>{i.forEach((e=>{e.dispose&&e.dispose()})),s(e)})).finally((()=>{o&&o.removeEventListener("abort",s)}))}))},a=function(e){let{signal:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise(((o,r)=>{const s=[];n&&n.addEventListener("abort",r,{once:!0});const a=Object.values(e).map((e=>e&&e.type&&t.has(e.type)?i([e],{signal:n}).then((e=>{let[n]=e;return s.push(n),n})):e)),l=Object.keys(e);Promise.all(a).then((e=>e.reduce(((e,n,t)=>(e[l[t]]=n,e)),{}))).then(o).catch((e=>{s.forEach((e=>{e.dispose&&e.dispose()})),r(e)})).finally((()=>{n&&n.removeEventListener("abort",r)}))}))};export{a as enlivenObjectEnlivables,i as enlivenObjects,s as loadImage};
//# sourceMappingURL=objectEnlive.min.mjs.map
