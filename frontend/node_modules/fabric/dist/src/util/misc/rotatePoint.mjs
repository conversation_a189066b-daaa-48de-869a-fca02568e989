/**
 * Rotates `point` around `origin` with `radians`
 * @deprecated use the Point.rotate
 * @param {Point} origin The origin of the rotation
 * @param {Point} origin The origin of the rotation
 * @param {TRadian} radians The radians of the angle for the rotation
 * @return {Point} The new rotated point
 */
const rotatePoint = (point, origin, radians) => point.rotate(radians, origin);

export { rotatePoint };
//# sourceMappingURL=rotatePoint.mjs.map
