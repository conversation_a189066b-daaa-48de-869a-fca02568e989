import type { TMat2D } from './typedefs';
export declare const VERSION: string;
export declare function noop(): void;
export declare const halfPI: number;
export declare const twoMathPi: number;
export declare const PiBy180: number;
export declare const iMatrix: TMat2D;
export declare const DEFAULT_SVG_FONT_SIZE = 16;
export declare const ALIASING_LIMIT = 2;
export declare const kRect: number;
export declare const CENTER = "center";
export declare const LEFT = "left";
export declare const TOP = "top";
export declare const BOTTOM = "bottom";
export declare const RIGHT = "right";
export declare const NONE = "none";
export declare const reNewline: RegExp;
export declare const MOVING = "moving";
export declare const SCALING = "scaling";
export declare const ROTATING = "rotating";
export declare const ROTATE = "rotate";
export declare const SKEWING = "skewing";
export declare const RESIZING = "resizing";
export declare const MODIFY_POLY = "modifyPoly";
export declare const MODIFY_PATH = "modifyPath";
export declare const CHANGED = "changed";
export declare const SCALE = "scale";
export declare const SCALE_X = "scaleX";
export declare const SCALE_Y = "scaleY";
export declare const SKEW_X = "skewX";
export declare const SKEW_Y = "skewY";
export declare const FILL = "fill";
export declare const STROKE = "stroke";
export declare const MODIFIED = "modified";
//# sourceMappingURL=constants.d.ts.map