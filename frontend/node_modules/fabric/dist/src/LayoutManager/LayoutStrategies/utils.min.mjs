import{Point as r,ZERO as t}from"../../Point.min.mjs";import{multiplyTransformMatrixArray as i}from"../../util/misc/matrix.min.mjs";import{sizeAfterTransform as o}from"../../util/misc/objectTransforms.min.mjs";import{calcPlaneChangeMatrix as m,sendVectorToPlane as n}from"../../util/misc/planeChange.min.mjs";const a=(a,e)=>{const{strokeUniform:s,strokeWidth:c,width:l,height:f,group:d}=e,u=d&&d!==a?m(d.calcTransformMatrix(),a.calcTransformMatrix()):null,p=u?e.getRelativeCenterPoint().transform(u):e.getRelativeCenterPoint(),x=!e.isStrokeAccountedForInDimensions(),g=s&&x?n(new r(c,c),void 0,a.calcTransformMatrix()):t,h=!s&&x?c:0,j=o(l+h,f+h,i([u,e.calcOwnMatrix()],!0)).add(g).scalarDivide(2);return[p.subtract(j),p.add(j)]};export{a as getObjectBounds};
//# sourceMappingURL=utils.min.mjs.map
