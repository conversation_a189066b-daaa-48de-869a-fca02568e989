import{defineProperty as t}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{removeFromArray as e}from"../util/internals/removeFromArray.min.mjs";class i{constructor(e){t(this,"targets",[]),t(this,"__disposer",void 0);const i=()=>{const{hiddenTextarea:t}=e.getActiveObject()||{};t&&t.focus()},s=e.upperCanvasEl;s.addEventListener("click",i),this.__disposer=()=>s.removeEventListener("click",i)}exitTextEditing(){this.target=void 0,this.targets.forEach((t=>{t.isEditing&&t.exitEditing()}))}add(t){this.targets.push(t)}remove(t){this.unregister(t),e(this.targets,t)}register(t){this.target=t}unregister(t){t===this.target&&(this.target=void 0)}onMouseMove(t){var e;(null===(e=this.target)||void 0===e?void 0:e.isEditing)&&this.target.updateSelectionOnMouseMove(t)}clear(){this.targets=[],this.target=void 0}dispose(){this.clear(),this.__disposer(),delete this.__disposer}}export{i as TextEditingManager};
//# sourceMappingURL=TextEditingManager.min.mjs.map
