import{defineProperty as t}from"../../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{getFabricDocument as e,getEnv as i}from"../../env/index.min.mjs";import{setCanvasDimensions as s,setCSSDimensions as a,getElementOffset as o}from"./util.min.mjs";import{isHTMLCanvas as r,createCanvasElement as l}from"../../util/misc/dom.min.mjs";import{FabricError as n}from"../../util/internals/console.min.mjs";class c{constructor(e){t(this,"_originalCanvasStyle",void 0),t(this,"lower",void 0);const i=this.createLowerCanvas(e);this.lower={el:i,ctx:i.getContext("2d")}}createLowerCanvas(t){const i=r(t)?t:t&&e().getElementById(t)||l();if(i.hasAttribute("data-fabric"))throw new n("Trying to initialize a canvas that has already been initialized. Did you forget to dispose the canvas?");return this._originalCanvasStyle=i.style.cssText,i.setAttribute("data-fabric","main"),i.classList.add("lower-canvas"),i}cleanupDOM(t){let{width:e,height:i}=t;const{el:s}=this.lower;s.classList.remove("lower-canvas"),s.removeAttribute("data-fabric"),s.setAttribute("width","".concat(e)),s.setAttribute("height","".concat(i)),s.style.cssText=this._originalCanvasStyle||"",this._originalCanvasStyle=void 0}setDimensions(t,e){const{el:i,ctx:a}=this.lower;s(i,a,t,e)}setCSSDimensions(t){a(this.lower.el,t)}calcOffset(){return o(this.lower.el)}dispose(){i().dispose(this.lower.el),delete this.lower}}export{c as StaticCanvasDOMManager};
//# sourceMappingURL=StaticCanvasDOMManager.min.mjs.map
