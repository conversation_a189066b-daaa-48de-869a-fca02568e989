import{NONE as t}from"../../constants.min.mjs";import{getDocumentFromElement as e,getWindowFromElement as n,getScrollLeftTop as o}from"../../util/dom_misc.min.mjs";const i=function(t,e,n){let{width:o,height:i}=n,l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;t.width=o,t.height=i,l>1&&(t.setAttribute("width",(o*l).toString()),t.setAttribute("height",(i*l).toString()),e.scale(l,l))},l=(t,e)=>{let{width:n,height:o}=e;n&&(t.style.width="number"==typeof n?"".concat(n,"px"):n),o&&(t.style.height="number"==typeof o?"".concat(o,"px"):o)};function r(t){var i;const l=t&&e(t),r={left:0,top:0};if(!l)return r;const p=(null===(i=n(t))||void 0===i?void 0:i.getComputedStyle(t,null))||{};r.left+=parseInt(p.borderLeftWidth,10)||0,r.top+=parseInt(p.borderTopWidth,10)||0,r.left+=parseInt(p.paddingLeft,10)||0,r.top+=parseInt(p.paddingTop,10)||0;let s={left:0,top:0};const c=l.documentElement;void 0!==t.getBoundingClientRect&&(s=t.getBoundingClientRect());const d=o(t);return{left:s.left+d.left-(c.clientLeft||0)+r.left,top:s.top+d.top-(c.clientTop||0)+r.top}}function p(e){return void 0!==e.onselectstart&&(e.onselectstart=()=>!1),e.style.userSelect=t,e}export{r as getElementOffset,p as makeElementUnselectable,l as setCSSDimensions,i as setCanvasDimensions};
//# sourceMappingURL=util.min.mjs.map
