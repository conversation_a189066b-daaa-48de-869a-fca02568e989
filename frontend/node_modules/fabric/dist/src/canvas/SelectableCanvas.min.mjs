import{defineProperty as t,objectSpread2 as e}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{dragHandler as i}from"../controls/drag.min.mjs";import{getActionFromCorner as s}from"../controls/util.min.mjs";import{Point as r}from"../Point.min.mjs";import{FabricObject as n}from"../shapes/Object/FabricObject.min.mjs";import{saveObjectTransform as o,addTransformToObject as c}from"../util/misc/objectTransforms.min.mjs";import{StaticCanvas as a}from"./StaticCanvas.min.mjs";import{isCollection as h}from"../Collection.min.mjs";import{isTransparent as l}from"../util/misc/isTransparent.min.mjs";import{degreesToRadians as d}from"../util/misc/radiansDegreesConversion.min.mjs";import{isTouchEvent as m,getPointer as g}from"../util/dom_event.min.mjs";import{pick as u}from"../util/misc/pick.min.mjs";import{sendPointToPlane as p}from"../util/misc/planeChange.min.mjs";import{cos as f}from"../util/misc/cos.min.mjs";import{sin as _}from"../util/misc/sin.min.mjs";import"../util/misc/vectors.min.mjs";import"../util/misc/projectStroke/StrokeLineJoinProjections.min.mjs";import{SCALE as v,SCALE_X as j,SCALE_Y as b,RESIZING as T,ROTATE as x,RIGHT as C,LEFT as O,BOTTOM as y,TOP as w,CENTER as S,MODIFIED as A,SKEW_X as P,SKEW_Y as D}from"../constants.min.mjs";import"../config.min.mjs";import{createCanvasElement as R}from"../util/misc/dom.min.mjs";import"../shapes/Group.min.mjs";import"../cache.min.mjs";import"../parser/constants.min.mjs";import"../util/animation/AnimationRegistry.min.mjs";import{CanvasDOMManager as k}from"./DOMManagers/CanvasDOMManager.min.mjs";import{canvasDefaults as F}from"./CanvasOptions.min.mjs";import{Intersection as M}from"../Intersection.min.mjs";import{isActiveSelection as E}from"../util/typeAssertions.min.mjs";class B extends a{constructor(){super(...arguments),t(this,"targets",[]),t(this,"_hoveredTargets",[]),t(this,"_objectsToRender",void 0),t(this,"_currentTransform",null),t(this,"_groupSelector",null),t(this,"contextTopDirty",!1)}static getDefaults(){return e(e({},super.getDefaults()),B.ownDefaults)}get upperCanvasEl(){var t;return null===(t=this.elements.upper)||void 0===t?void 0:t.el}get contextTop(){var t;return null===(t=this.elements.upper)||void 0===t?void 0:t.ctx}get wrapperEl(){return this.elements.container}initElements(t){this.elements=new k(t,{allowTouchScrolling:this.allowTouchScrolling,containerClass:this.containerClass}),this._createCacheCanvas()}_onObjectAdded(t){this._objectsToRender=void 0,super._onObjectAdded(t)}_onObjectRemoved(t){this._objectsToRender=void 0,t===this._activeObject&&(this.fire("before:selection:cleared",{deselected:[t]}),this._discardActiveObject(),this.fire("selection:cleared",{deselected:[t]}),t.fire("deselected",{target:t})),t===this._hoveredTarget&&(this._hoveredTarget=void 0,this._hoveredTargets=[]),super._onObjectRemoved(t)}_onStackOrderChanged(){this._objectsToRender=void 0,super._onStackOrderChanged()}_chooseObjectsToRender(){const t=this._activeObject;return!this.preserveObjectStacking&&t?this._objects.filter((e=>!e.group&&e!==t)).concat(t):this._objects}renderAll(){this.cancelRequestedRender(),this.destroyed||(!this.contextTopDirty||this._groupSelector||this.isDrawingMode||(this.clearContext(this.contextTop),this.contextTopDirty=!1),this.hasLostContext&&(this.renderTopLayer(this.contextTop),this.hasLostContext=!1),!this._objectsToRender&&(this._objectsToRender=this._chooseObjectsToRender()),this.renderCanvas(this.getContext(),this._objectsToRender))}renderTopLayer(t){t.save(),this.isDrawingMode&&this._isCurrentlyDrawing&&(this.freeDrawingBrush&&this.freeDrawingBrush._render(),this.contextTopDirty=!0),this.selection&&this._groupSelector&&(this._drawSelection(t),this.contextTopDirty=!0),t.restore()}renderTop(){const t=this.contextTop;this.clearContext(t),this.renderTopLayer(t),this.fire("after:render",{ctx:t})}setTargetFindTolerance(t){t=Math.round(t),this.targetFindTolerance=t;const e=this.getRetinaScaling(),i=Math.ceil((2*t+1)*e);this.pixelFindCanvasEl.width=this.pixelFindCanvasEl.height=i,this.pixelFindContext.scale(e,e)}isTargetTransparent(t,e,i){const s=this.targetFindTolerance,r=this.pixelFindContext;this.clearContext(r),r.save(),r.translate(-e+s,-i+s),r.transform(...this.viewportTransform);const n=t.selectionBackgroundColor;t.selectionBackgroundColor="",t.render(r),t.selectionBackgroundColor=n,r.restore();const o=Math.round(s*this.getRetinaScaling());return l(r,o,o,o)}_isSelectionKeyPressed(t){const e=this.selectionKey;return!!e&&(Array.isArray(e)?!!e.find((e=>!!e&&!0===t[e])):t[e])}_shouldClearSelection(t,e){const i=this.getActiveObjects(),s=this._activeObject;return!!(!e||e&&s&&i.length>1&&-1===i.indexOf(e)&&s!==e&&!this._isSelectionKeyPressed(t)||e&&!e.evented||e&&!e.selectable&&s&&s!==e)}_shouldCenterTransform(t,e,i){if(!t)return;let s;return e===v||e===j||e===b||e===T?s=this.centeredScaling||t.centeredScaling:e===x&&(s=this.centeredRotation||t.centeredRotation),s?!i:i}_getOriginFromCorner(t,e){const i={x:t.originX,y:t.originY};return e?(["ml","tl","bl"].includes(e)?i.x=C:["mr","tr","br"].includes(e)&&(i.x=O),["tl","mt","tr"].includes(e)?i.y=y:["bl","mb","br"].includes(e)&&(i.y=w),i):i}_setupCurrentTransform(t,r,n){var c;const a=r.group?p(this.getScenePoint(t),void 0,r.group.calcTransformMatrix()):this.getScenePoint(t),{key:h="",control:l}=r.getActiveControl()||{},m=n&&l?null===(c=l.getActionHandler(t,r,l))||void 0===c?void 0:c.bind(l):i,g=s(n,h,t,r),u=t[this.centeredKey],f=this._shouldCenterTransform(r,g,u)?{x:S,y:S}:this._getOriginFromCorner(r,h),_={target:r,action:g,actionHandler:m,actionPerformed:!1,corner:h,scaleX:r.scaleX,scaleY:r.scaleY,skewX:r.skewX,skewY:r.skewY,offsetX:a.x-r.left,offsetY:a.y-r.top,originX:f.x,originY:f.y,ex:a.x,ey:a.y,lastX:a.x,lastY:a.y,theta:d(r.angle),width:r.width,height:r.height,shiftKey:t.shiftKey,altKey:u,original:e(e({},o(r)),{},{originX:f.x,originY:f.y})};this._currentTransform=_,this.fire("before:transform",{e:t,transform:_})}setCursor(t){this.upperCanvasEl.style.cursor=t}_drawSelection(t){const{x:e,y:i,deltaX:s,deltaY:o}=this._groupSelector,c=new r(e,i).transform(this.viewportTransform),a=new r(e+s,i+o).transform(this.viewportTransform),h=this.selectionLineWidth/2;let l=Math.min(c.x,a.x),d=Math.min(c.y,a.y),m=Math.max(c.x,a.x),g=Math.max(c.y,a.y);this.selectionColor&&(t.fillStyle=this.selectionColor,t.fillRect(l,d,m-l,g-d)),this.selectionLineWidth&&this.selectionBorderColor&&(t.lineWidth=this.selectionLineWidth,t.strokeStyle=this.selectionBorderColor,l+=h,d+=h,m-=h,g-=h,n.prototype._setLineDash.call(this,t,this.selectionDashArray),t.strokeRect(l,d,m-l,g-d))}findTarget(t){if(this.skipTargetFind)return;const e=this.getViewportPoint(t),i=this._activeObject,s=this.getActiveObjects();if(this.targets=[],i&&s.length>=1){if(i.findControl(e,m(t)))return i;if(s.length>1&&this.searchPossibleTargets([i],e))return i;if(i===this.searchPossibleTargets([i],e)){if(this.preserveObjectStacking){const s=this.targets;this.targets=[];const r=this.searchPossibleTargets(this._objects,e);return t[this.altSelectionKey]&&r&&r!==i?(this.targets=s,i):r}return i}}return this.searchPossibleTargets(this._objects,e)}_pointIsInObjectSelectionArea(t,e){let i=t.getCoords();const s=this.getZoom(),n=t.padding/s;if(n){const[t,e,s,o]=i,c=Math.atan2(e.y-t.y,e.x-t.x),a=f(c)*n,h=_(c)*n,l=a+h,d=a-h;i=[new r(t.x-d,t.y-l),new r(e.x+l,e.y-d),new r(s.x+d,s.y+l),new r(o.x-l,o.y+d)]}return M.isPointInPolygon(e,i)}_checkTarget(t,e){if(t&&t.visible&&t.evented&&this._pointIsInObjectSelectionArea(t,p(e,void 0,this.viewportTransform))){if(!this.perPixelTargetFind&&!t.perPixelTargetFind||t.isEditing)return!0;if(!this.isTargetTransparent(t,e.x,e.y))return!0}return!1}_searchPossibleTargets(t,e){let i=t.length;for(;i--;){const s=t[i];if(this._checkTarget(s,e)){if(h(s)&&s.subTargetCheck){const t=this._searchPossibleTargets(s._objects,e);t&&this.targets.push(t)}return s}}}searchPossibleTargets(t,e){const i=this._searchPossibleTargets(t,e);if(i&&h(i)&&i.interactive&&this.targets[0]){const t=this.targets;for(let e=t.length-1;e>0;e--){const i=t[e];if(!h(i)||!i.interactive)return i}return t[0]}return i}getViewportPoint(t){return this._pointer?this._pointer:this.getPointer(t,!0)}getScenePoint(t){return this._absolutePointer?this._absolutePointer:this.getPointer(t)}getPointer(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=this.upperCanvasEl,s=i.getBoundingClientRect();let n=g(t),o=s.width||0,c=s.height||0;o&&c||(w in s&&y in s&&(c=Math.abs(s.top-s.bottom)),C in s&&O in s&&(o=Math.abs(s.right-s.left))),this.calcOffset(),n.x=n.x-this._offset.left,n.y=n.y-this._offset.top,e||(n=p(n,void 0,this.viewportTransform));const a=this.getRetinaScaling();1!==a&&(n.x/=a,n.y/=a);const h=0===o||0===c?new r(1,1):new r(i.width/o,i.height/c);return n.multiply(h)}_setDimensionsImpl(t,e){this._resetTransformEventData(),super._setDimensionsImpl(t,e),this._isCurrentlyDrawing&&this.freeDrawingBrush&&this.freeDrawingBrush._setBrushStyles(this.contextTop)}_createCacheCanvas(){this.pixelFindCanvasEl=R(),this.pixelFindContext=this.pixelFindCanvasEl.getContext("2d",{willReadFrequently:!0}),this.setTargetFindTolerance(this.targetFindTolerance)}getTopContext(){return this.elements.upper.ctx}getSelectionContext(){return this.elements.upper.ctx}getSelectionElement(){return this.elements.upper.el}getActiveObject(){return this._activeObject}getActiveObjects(){const t=this._activeObject;return E(t)?t.getObjects():t?[t]:[]}_fireSelectionEvents(t,e){let i=!1,s=!1;const r=this.getActiveObjects(),n=[],o=[];t.forEach((t=>{r.includes(t)||(i=!0,t.fire("deselected",{e:e,target:t}),o.push(t))})),r.forEach((s=>{t.includes(s)||(i=!0,s.fire("selected",{e:e,target:s}),n.push(s))})),t.length>0&&r.length>0?(s=!0,i&&this.fire("selection:updated",{e:e,selected:n,deselected:o})):r.length>0?(s=!0,this.fire("selection:created",{e:e,selected:n})):t.length>0&&(s=!0,this.fire("selection:cleared",{e:e,deselected:o})),s&&(this._objectsToRender=void 0)}setActiveObject(t,e){const i=this.getActiveObjects(),s=this._setActiveObject(t,e);return this._fireSelectionEvents(i,e),s}_setActiveObject(t,e){const i=this._activeObject;return i!==t&&(!(!this._discardActiveObject(e,t)&&this._activeObject)&&(!t.onSelect({e:e})&&(this._activeObject=t,E(t)&&i!==t&&t.set("canvas",this),t.setCoords(),!0)))}_discardActiveObject(t,e){const i=this._activeObject;return!!i&&(!i.onDeselect({e:t,object:e})&&(this._currentTransform&&this._currentTransform.target===i&&this.endCurrentTransform(t),E(i)&&i===this._hoveredTarget&&(this._hoveredTarget=void 0),this._activeObject=void 0,!0))}discardActiveObject(t){const e=this.getActiveObjects(),i=this.getActiveObject();e.length&&this.fire("before:selection:cleared",{e:t,deselected:[i]});const s=this._discardActiveObject(t);return this._fireSelectionEvents(e,t),s}endCurrentTransform(t){const e=this._currentTransform;this._finalizeCurrentTransform(t),e&&e.target&&(e.target.isMoving=!1),this._currentTransform=null}_finalizeCurrentTransform(t){const e=this._currentTransform,i=e.target,s={e:t,target:i,transform:e,action:e.action};i._scaling&&(i._scaling=!1),i.setCoords(),e.actionPerformed&&(this.fire("object:modified",s),i.fire(A,s))}setViewportTransform(t){super.setViewportTransform(t);const e=this._activeObject;e&&e.setCoords()}destroy(){const t=this._activeObject;E(t)&&(t.removeAll(),t.dispose()),delete this._activeObject,super.destroy(),this.pixelFindContext=null,this.pixelFindCanvasEl=void 0}clear(){this.discardActiveObject(),this._activeObject=void 0,this.clearContext(this.contextTop),super.clear()}drawControls(t){const e=this._activeObject;e&&e._renderControls(t)}_toObject(t,e,i){const s=this._realizeGroupTransformOnObject(t),r=super._toObject(t,e,i);return t.set(s),r}_realizeGroupTransformOnObject(t){const{group:e}=t;if(e&&E(e)&&this._activeObject===e){const i=u(t,["angle","flipX","flipY",O,j,b,P,D,w]);return c(t,e.calcOwnMatrix()),i}return{}}_setSVGObject(t,e,i){const s=this._realizeGroupTransformOnObject(e);super._setSVGObject(t,e,i),e.set(s)}}t(B,"ownDefaults",F);export{B as SelectableCanvas};
//# sourceMappingURL=SelectableCanvas.min.mjs.map
