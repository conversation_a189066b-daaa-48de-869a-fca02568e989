import{NOT_ALLOWED_CURSOR as n,findCornerQuadrant as i,isLocked as s,getLocalPoint as e,isTransformCentered as t,invertOrigin as r}from"./util.min.mjs";import{wrapWithFireEvent as a}from"./wrapWithFireEvent.min.mjs";import{wrapWithFixedAnchor as c}from"./wrapWithFixedAnchor.min.mjs";import{SCALING as o,SCALE_X as l,SCALE_Y as g}from"../constants.min.mjs";function u(n,i){const s=i.canvas,e=n[s.uniScaleKey];return s.uniformScaling&&!e||!s.uniformScaling&&e}function m(n,i,e){const t=s(n,"lockScalingX"),r=s(n,"lockScalingY");if(t&&r)return!0;if(!i&&(t||r)&&e)return!0;if(t&&"x"===i)return!0;if(r&&"y"===i)return!0;const{width:a,height:c,strokeWidth:o}=n;return 0===a&&0===o&&"y"!==i||0===c&&0===o&&"x"!==i}const f=["e","se","s","sw","w","nw","n","ne","e"],y=(s,e,t)=>{const r=u(s,t);if(m(t,0!==e.x&&0===e.y?"x":0===e.x&&0!==e.y?"y":"",r))return n;const a=i(t,e);return"".concat(f[a],"-resize")};function X(n,i,a,c){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};const f=i.target,y=o.by,X=u(n,f);let Y,h,x,S,b,p;if(m(f,y,X))return!1;if(i.gestureScale)h=i.scaleX*i.gestureScale,x=i.scaleY*i.gestureScale;else{if(Y=e(i,i.originX,i.originY,a,c),b="y"!==y?Math.sign(Y.x||i.signX||1):1,p="x"!==y?Math.sign(Y.y||i.signY||1):1,i.signX||(i.signX=b),i.signY||(i.signY=p),s(f,"lockScalingFlip")&&(i.signX!==b||i.signY!==p))return!1;if(S=f._getTransformedDimensions(),X&&!y){const n=Math.abs(Y.x)+Math.abs(Y.y),{original:s}=i,e=n/(Math.abs(S.x*s.scaleX/f.scaleX)+Math.abs(S.y*s.scaleY/f.scaleY));h=s.scaleX*e,x=s.scaleY*e}else h=Math.abs(Y.x*f.scaleX/S.x),x=Math.abs(Y.y*f.scaleY/S.y);t(i)&&(h*=2,x*=2),i.signX!==b&&"y"!==y&&(i.originX=r(i.originX),h*=-1,i.signX=b),i.signY!==p&&"x"!==y&&(i.originY=r(i.originY),x*=-1,i.signY=p)}const M=f.scaleX,k=f.scaleY;return y?("x"===y&&f.set(l,h),"y"===y&&f.set(g,x)):(!s(f,"lockScalingX")&&f.set(l,h),!s(f,"lockScalingY")&&f.set(g,x)),M!==f.scaleX||k!==f.scaleY}const Y=(n,i,s,e)=>X(n,i,s,e),h=a(o,c(Y)),x=a(o,c(((n,i,s,e)=>X(n,i,s,e,{by:"x"})))),S=a(o,c(((n,i,s,e)=>X(n,i,s,e,{by:"y"}))));export{y as scaleCursorStyleHandler,u as scaleIsProportional,Y as scaleObjectFromCorner,h as scalingEqually,m as scalingIsForbidden,x as scalingX,S as scalingY};
//# sourceMappingURL=scale.min.mjs.map
