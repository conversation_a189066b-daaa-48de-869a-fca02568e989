import{objectSpread2 as t}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{Point as n}from"../Point.min.mjs";import{Control as o}from"./Control.min.mjs";import{multiplyTransformMatrices as e}from"../util/misc/matrix.min.mjs";import{sendPointToPlane as r}from"../util/misc/planeChange.min.mjs";import{fireEvent as c}from"./fireEvent.min.mjs";import{commonEventInfo as s}from"./util.min.mjs";const i=(t,o,r)=>{const{path:c,pathOffset:s}=t,i=c[o];return new n(i[r]-s.x,i[r+1]-s.y).transform(e(t.getViewportTransform(),t.calcTransformMatrix()))};function a(t,n,o){const{commandIndex:e,pointIndex:r}=this;return i(o,e,r)}function m(o,e,i,a){const{target:m}=e,{commandIndex:l,pointIndex:d}=this,p=((t,o,e,c,s)=>{const{path:i,pathOffset:a}=t,m=i[(c>0?c:i.length)-1],l=new n(m[s],m[s+1]),d=l.subtract(a).transform(t.calcOwnMatrix()),p=r(new n(o,e),void 0,t.calcOwnMatrix());i[c][s]=p.x+a.x,i[c][s+1]=p.y+a.y,t.setDimensions();const x=l.subtract(t.pathOffset).transform(t.calcOwnMatrix()).subtract(d);return t.left-=x.x,t.top-=x.y,t.set("dirty",!0),!0})(m,i,a,l,d);return c(this.actionName,t(t({},s(o,e,i,a)),{},{commandIndex:l,pointIndex:d})),p}class l extends o{constructor(t){super(t)}render(n,o,e,r,c){const s=t(t({},r),{},{cornerColor:this.controlFill,cornerStrokeColor:this.controlStroke,transparentCorners:!this.controlFill});super.render(n,o,e,s,c)}}class d extends l{constructor(t){super(t)}render(t,n,o,e,r){const{path:c}=r,{commandIndex:s,pointIndex:a,connectToCommandIndex:m,connectToPointIndex:l}=this;t.save(),t.strokeStyle=this.controlStroke,this.connectionDashArray&&t.setLineDash(this.connectionDashArray);const[d]=c[s],p=i(r,m,l);if("Q"===d){const e=i(r,s,a+2);t.moveTo(e.x,e.y),t.lineTo(n,o)}else t.moveTo(n,o);t.lineTo(p.x,p.y),t.stroke(),t.restore(),super.render(t,n,o,e,r)}}const p=(n,o,e,r,c,s)=>new(e?d:l)(t(t({commandIndex:n,pointIndex:o,actionName:"modifyPath",positionHandler:a,actionHandler:m,connectToCommandIndex:c,connectToPointIndex:s},r),e?r.controlPointStyle:r.pointStyle));function x(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const o={};let e="M";return t.path.forEach(((t,r)=>{const c=t[0];switch("Z"!==c&&(o["c_".concat(r,"_").concat(c)]=p(r,t.length-2,!1,n)),c){case"C":o["c_".concat(r,"_C_CP_1")]=p(r,1,!0,n,r-1,(t=>"C"===t?5:"Q"===t?3:1)(e)),o["c_".concat(r,"_C_CP_2")]=p(r,3,!0,n,r,5);break;case"Q":o["c_".concat(r,"_Q_CP_1")]=p(r,1,!0,n,r,3)}e=c})),o}export{x as createPathControls};
//# sourceMappingURL=pathControl.min.mjs.map
