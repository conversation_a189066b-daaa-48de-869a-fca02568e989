import{SKEW_X as t,SCALE_Y as n,SKEW_Y as r,SCALE_X as m}from"../constants.min.mjs";import{scaleCursorStyleHandler as o,scalingX as s,scalingY as e}from"./scale.min.mjs";import{skewCursorStyleHandler as i,skewHandlerY as a,skewHandlerX as c}from"./skew.min.mjs";function f(t,n){return t[n.canvas.altActionKey]}const p=(o,s,e)=>{const i=f(o,e);return 0===s.x?i?t:n:0===s.y?i?r:m:""},j=(t,n,r)=>f(t,r)?i(t,n,r):o(t,n,r),u=(t,n,r,m)=>f(t,n.target)?a(t,n,r,m):s(t,n,r,m),g=(t,n,r,m)=>f(t,n.target)?c(t,n,r,m):e(t,n,r,m);export{p as scaleOrSkewActionName,j as scaleSkewCursorStyleHandler,u as scalingXOrSkewingY,g as scalingYOrSkewingX};
//# sourceMappingURL=scaleSkew.min.mjs.map
