import{resolveOrigin as n}from"../util/misc/resolveOrigin.min.mjs";import{Point as t}from"../Point.min.mjs";import{radiansToDegrees as o,degreesToRadians as r}from"../util/misc/radiansDegreesConversion.min.mjs";import{CENTER as e}from"../constants.min.mjs";const i="not-allowed",s=(n,t,o,r)=>{if(!t||!n)return"drag";const e=r.controls[t];return e.getActionName(o,e,r)};function a(t){return n(t.originX)===n(e)&&n(t.originY)===n(e)}function m(t){return.5-n(t)}const c=(n,t)=>n[t],l=(n,o,r,e)=>({e:n,transform:o,pointer:new t(r,e)});function u(n,t){const r=n.getTotalAngle()+o(Math.atan2(t.y,t.x))+360;return Math.round(r%360/45)}function f(n,o,i,s,a){var m;let{target:c,corner:l}=n;const u=c.controls[l],f=(null===(m=c.canvas)||void 0===m?void 0:m.getZoom())||1,g=c.padding/f,d=function(n,o,i,s){const a=n.getRelativeCenterPoint(),m=void 0!==i&&void 0!==s?n.translateToGivenOrigin(a,e,e,i,s):new t(n.left,n.top);return(n.angle?o.rotate(-r(n.angle),a):o).subtract(m)}(c,new t(s,a),o,i);return d.x>=g&&(d.x-=g),d.x<=-g&&(d.x+=g),d.y>=g&&(d.y-=g),d.y<=g&&(d.y+=g),d.x-=u.offsetX,d.y-=u.offsetY,d}export{i as NOT_ALLOWED_CURSOR,l as commonEventInfo,u as findCornerQuadrant,s as getActionFromCorner,f as getLocalPoint,m as invertOrigin,c as isLocked,a as isTransformCentered};
//# sourceMappingURL=util.min.mjs.map
