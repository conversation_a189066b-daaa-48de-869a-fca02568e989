import{ROTATING as t}from"../constants.min.mjs";import{radiansToDegrees as n}from"../util/misc/radiansDegreesConversion.min.mjs";import{NOT_ALLOWED_CURSOR as o,isLocked as i}from"./util.min.mjs";import{wrapWithFireEvent as r}from"./wrapWithFireEvent.min.mjs";import{wrapWithFixedAnchor as a}from"./wrapWithFixedAnchor.min.mjs";const e=(t,n,i)=>i.lockRotation?o:n.cursorStyle,s=r(t,a(((t,o,r,a)=>{let{target:e,ex:s,ey:m,theta:l,originX:c,originY:h}=o;const p=e.translateToOriginPoint(e.getRelativeCenterPoint(),c,h);if(i(e,"lockRotation"))return!1;const g=Math.atan2(m-p.y,s-p.x),f=Math.atan2(a-p.y,r-p.x);let M=n(f-g+l);if(e.snapAngle&&e.snapAngle>0){const t=e.snapAngle,n=e.snapThreshold||t,o=Math.ceil(M/t)*t,i=Math.floor(M/t)*t;Math.abs(M-i)<n?M=i:Math.abs(M-o)<n&&(M=o)}M<0&&(M=360+M),M%=360;const j=e.angle!==M;return e.angle=M,j})));export{e as rotationStyleHandler,s as rotationWithSnapping};
//# sourceMappingURL=rotate.min.mjs.map
