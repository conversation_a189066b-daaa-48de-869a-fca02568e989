import{objectSpread2 as t}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{Point as n}from"../Point.min.mjs";import{Control as r}from"./Control.min.mjs";import{multiplyTransformMatrices as o}from"../util/misc/matrix.min.mjs";import{wrapWithFireEvent as i}from"./wrapWithFireEvent.min.mjs";import{sendPointToPlane as e}from"../util/misc/planeChange.min.mjs";import{MODIFY_POLY as s}from"../constants.min.mjs";const a=s,m=t=>function(r,i,e){const{points:s,pathOffset:a}=e;return new n(s[t]).subtract(a).transform(o(e.getViewportTransform(),e.calcTransformMatrix()))},p=(t,r,o,i)=>{const{target:s,pointIndex:a}=r,m=s,p=e(new n(o,i),void 0,m.calcOwnMatrix());return m.points[a]=p.add(m.pathOffset),m.setDimensions(),m.set("dirty",!0),!0},c=(r,o)=>function(i,e,s,a){const m=e.target,p=new n(m.points[(r>0?r:m.points.length)-1]),c=p.subtract(m.pathOffset).transform(m.calcOwnMatrix()),f=o(i,t(t({},e),{},{pointIndex:r}),s,a),l=p.subtract(m.pathOffset).transform(m.calcOwnMatrix()).subtract(c);return m.left-=l.x,m.top-=l.y,f},f=t=>i(a,c(t,p));function l(n){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i={};for(let e=0;e<("number"==typeof n?n:n.points.length);e++)i["p".concat(e)]=new r(t({actionName:a,positionHandler:m(e),actionHandler:f(e)},o));return i}export{f as createPolyActionHandler,l as createPolyControls,m as createPolyPositionHandler,c as factoryPolyActionHandler,p as polyActionHandler};
//# sourceMappingURL=polyControl.min.mjs.map
