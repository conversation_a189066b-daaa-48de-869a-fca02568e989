import{objectSpread2 as e,objectWithoutProperties as i}from"../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{resolveOrigin as n}from"../util/misc/resolveOrigin.min.mjs";import{Point as s}from"../Point.min.mjs";import{radiansToDegrees as r,degreesToRadians as t}from"../util/misc/radiansDegreesConversion.min.mjs";import{isLocked as o,NOT_ALLOWED_CURSOR as m,findCornerQuadrant as c,getLocalPoint as a}from"./util.min.mjs";import{wrapWithFireEvent as l}from"./wrapWithFireEvent.min.mjs";import{wrapWithFixedAnchor as g}from"./wrapWithFixedAnchor.min.mjs";import{CENTER as w,SKEWING as f,SCALE_X as k,SKEW_X as p,SCALE_Y as u,SKEW_Y as x}from"../constants.min.mjs";const d=["target","ex","ey","skewingSide"],S={x:{counterAxis:"y",scale:k,skew:p,lockSkewing:"lockSkewingX",origin:"originX",flip:"flipX"},y:{counterAxis:"x",scale:u,skew:x,lockSkewing:"lockSkewingY",origin:"originY",flip:"flipY"}},j=["ns","nesw","ew","nwse"],X=(e,i,n)=>{if(0!==i.x&&o(n,"lockSkewingY"))return m;if(0!==i.y&&o(n,"lockSkewingX"))return m;const s=c(n,i)%4;return"".concat(j[s],"-resize")};function y(m,c,p,u,x){const{target:j}=p,{counterAxis:X,origin:y,lockSkewing:h,skew:Y,flip:_}=S[m];if(o(j,h))return!1;const{origin:v,flip:D}=S[X],b=n(p[v])*(j[D]?-1:1),A=-Math.sign(b)*(j[_]?-1:1),M=.5*-((0===j[Y]&&a(p,w,w,u,x)[m]>0||j[Y]>0?1:-1)*A)+.5,T=l(f,g(((e,n,o,c)=>function(e,n,o){let{target:m,ex:c,ey:a,skewingSide:l}=n,g=i(n,d);const{skew:w}=S[e],f=o.subtract(new s(c,a)).divide(new s(m.scaleX,m.scaleY))[e],p=m[w],u=g[w],x=Math.tan(t(u)),j="y"===e?m._getTransformedDimensions({scaleX:1,scaleY:1,skewX:0}).x:m._getTransformedDimensions({scaleX:1,scaleY:1}).y,X=2*f*l/Math.max(j,1)+x,y=r(Math.atan(X));m.set(w,y);const h=p!==m[w];if(h&&"y"===e){const{skewX:e,scaleX:i}=m,n=m._getTransformedDimensions({skewY:p}),s=m._getTransformedDimensions(),r=0!==e?n.x/s.x:1;1!==r&&m.set(k,r*i)}return h}(m,n,new s(o,c)))));return T(c,e(e({},p),{},{[y]:M,skewingSide:A}),u,x)}const h=(e,i,n,s)=>y("x",e,i,n,s),Y=(e,i,n,s)=>y("y",e,i,n,s);export{X as skewCursorStyleHandler,h as skewHandlerX,Y as skewHandlerY};
//# sourceMappingURL=skew.min.mjs.map
