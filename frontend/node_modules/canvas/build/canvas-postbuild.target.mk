# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := canvas-postbuild
### Rules for final target.
LDFLAGS_Debug := \
	-pthread \
	-rdynamic \
	-m64

LDFLAGS_Release := \
	-pthread \
	-rdynamic \
	-m64

LIBS :=

$(obj).target/canvas-postbuild.node: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(obj).target/canvas-postbuild.node: LIBS := $(LIBS)
$(obj).target/canvas-postbuild.node: TOOLSET := $(TOOLSET)
$(obj).target/canvas-postbuild.node:  FORCE_DO_CMD
	$(call do_cmd,solink_module)

all_deps += $(obj).target/canvas-postbuild.node
# Add target alias
.PHONY: canvas-postbuild
canvas-postbuild: $(builddir)/canvas-postbuild.node

# Copy this to the executable output path.
$(builddir)/canvas-postbuild.node: TOOLSET := $(TOOLSET)
$(builddir)/canvas-postbuild.node: $(obj).target/canvas-postbuild.node FORCE_DO_CMD
	$(call do_cmd,copy)

all_deps += $(builddir)/canvas-postbuild.node
# Short alias for building this executable.
.PHONY: canvas-postbuild.node
canvas-postbuild.node: $(obj).target/canvas-postbuild.node $(builddir)/canvas-postbuild.node

# Add executable to "all" target.
.PHONY: all
all: $(builddir)/canvas-postbuild.node

