# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := canvas
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=canvas' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DHAVE_JPEG' \
	'-DHAVE_GIF' \
	'-DHAVE_RSVG' \
	'-DBUILDING_NODE_EXTENSION' \
	'-DDEBUG' \
	'-D_DEBUG'

# Flags passed to all source files.
CFLAGS_Debug := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-Wno-cast-function-type \
	-m64 \
	-g \
	-O0

# Flags passed to only C files.
CFLAGS_C_Debug :=

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-fno-rtti \
	-fno-strict-aliasing \
	-std=gnu++20

INCS_Debug := \
	-I/home/<USER>/.cache/node-gyp/23.9.0/include/node \
	-I/home/<USER>/.cache/node-gyp/23.9.0/src \
	-I/home/<USER>/.cache/node-gyp/23.9.0/deps/openssl/config \
	-I/home/<USER>/.cache/node-gyp/23.9.0/deps/openssl/openssl/include \
	-I/home/<USER>/.cache/node-gyp/23.9.0/deps/uv/include \
	-I/home/<USER>/.cache/node-gyp/23.9.0/deps/zlib \
	-I/home/<USER>/.cache/node-gyp/23.9.0/deps/v8/include \
	-I$(srcdir)/../nan \
	-I/usr/include/cairo \
	-I/usr/include/freetype2 \
	-I/usr/include/libpng16 \
	-I/usr/include/harfbuzz \
	-I/usr/include/glib-2.0 \
	-I/usr/lib/glib-2.0/include \
	-I/usr/include/sysprof-6 \
	-I/usr/include/pixman-1 \
	-I/usr/include/pango-1.0 \
	-I/usr/include/libmount \
	-I/usr/include/blkid \
	-I/usr/include/fribidi \
	-I/opt/homebrew/include \
	-I/usr/include/librsvg-2.0 \
	-I/usr/include/gdk-pixbuf-2.0 \
	-I/usr/include/libxml2

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=canvas' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DHAVE_JPEG' \
	'-DHAVE_GIF' \
	'-DHAVE_RSVG' \
	'-DBUILDING_NODE_EXTENSION'

# Flags passed to all source files.
CFLAGS_Release := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-Wno-cast-function-type \
	-m64 \
	-O3 \
	-fno-omit-frame-pointer

# Flags passed to only C files.
CFLAGS_C_Release :=

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-fno-rtti \
	-fno-strict-aliasing \
	-std=gnu++20

INCS_Release := \
	-I/home/<USER>/.cache/node-gyp/23.9.0/include/node \
	-I/home/<USER>/.cache/node-gyp/23.9.0/src \
	-I/home/<USER>/.cache/node-gyp/23.9.0/deps/openssl/config \
	-I/home/<USER>/.cache/node-gyp/23.9.0/deps/openssl/openssl/include \
	-I/home/<USER>/.cache/node-gyp/23.9.0/deps/uv/include \
	-I/home/<USER>/.cache/node-gyp/23.9.0/deps/zlib \
	-I/home/<USER>/.cache/node-gyp/23.9.0/deps/v8/include \
	-I$(srcdir)/../nan \
	-I/usr/include/cairo \
	-I/usr/include/freetype2 \
	-I/usr/include/libpng16 \
	-I/usr/include/harfbuzz \
	-I/usr/include/glib-2.0 \
	-I/usr/lib/glib-2.0/include \
	-I/usr/include/sysprof-6 \
	-I/usr/include/pixman-1 \
	-I/usr/include/pango-1.0 \
	-I/usr/include/libmount \
	-I/usr/include/blkid \
	-I/usr/include/fribidi \
	-I/opt/homebrew/include \
	-I/usr/include/librsvg-2.0 \
	-I/usr/include/gdk-pixbuf-2.0 \
	-I/usr/include/libxml2

OBJS := \
	$(obj).target/$(TARGET)/src/backend/Backend.o \
	$(obj).target/$(TARGET)/src/backend/ImageBackend.o \
	$(obj).target/$(TARGET)/src/backend/PdfBackend.o \
	$(obj).target/$(TARGET)/src/backend/SvgBackend.o \
	$(obj).target/$(TARGET)/src/bmp/BMPParser.o \
	$(obj).target/$(TARGET)/src/Backends.o \
	$(obj).target/$(TARGET)/src/Canvas.o \
	$(obj).target/$(TARGET)/src/CanvasGradient.o \
	$(obj).target/$(TARGET)/src/CanvasPattern.o \
	$(obj).target/$(TARGET)/src/CanvasRenderingContext2d.o \
	$(obj).target/$(TARGET)/src/closure.o \
	$(obj).target/$(TARGET)/src/color.o \
	$(obj).target/$(TARGET)/src/Image.o \
	$(obj).target/$(TARGET)/src/ImageData.o \
	$(obj).target/$(TARGET)/src/init.o \
	$(obj).target/$(TARGET)/src/register_font.o

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)

# CFLAGS et al overrides must be target-local.
# See "Target-specific Variable Values" in the GNU Make manual.
$(OBJS): TOOLSET := $(TOOLSET)
$(OBJS): GYP_CFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE))
$(OBJS): GYP_CXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE))

# Suffix rules, putting all outputs into $(obj).

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(srcdir)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

# Try building from generated source, too.

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj).$(TOOLSET)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

# End of this set of suffix rules
### Rules for final target.
LDFLAGS_Debug := \
	-pthread \
	-rdynamic \
	-m64

LDFLAGS_Release := \
	-pthread \
	-rdynamic \
	-m64

LIBS := \
	-lpixman-1 \
	-lcairo \
	-lpng16 \
	-lpangocairo-1.0 \
	-lpango-1.0 \
	-lharfbuzz \
	-lgobject-2.0 \
	-lglib-2.0 \
	-lfreetype \
	-ljpeg \
	-L/opt/homebrew/lib \
	-lgif \
	-lrsvg-2 \
	-lgdk_pixbuf-2.0 \
	-lgio-2.0

$(obj).target/canvas.node: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(obj).target/canvas.node: LIBS := $(LIBS)
$(obj).target/canvas.node: TOOLSET := $(TOOLSET)
$(obj).target/canvas.node: $(OBJS) FORCE_DO_CMD
	$(call do_cmd,solink_module)

all_deps += $(obj).target/canvas.node
# Add target alias
.PHONY: canvas
canvas: $(builddir)/canvas.node

# Copy this to the executable output path.
$(builddir)/canvas.node: TOOLSET := $(TOOLSET)
$(builddir)/canvas.node: $(obj).target/canvas.node FORCE_DO_CMD
	$(call do_cmd,copy)

all_deps += $(builddir)/canvas.node
# Short alias for building this executable.
.PHONY: canvas.node
canvas.node: $(obj).target/canvas.node $(builddir)/canvas.node

# Add executable to "all" target.
.PHONY: all
all: $(builddir)/canvas.node

