version: '3'

services:
  payload:
    image: node:18-alpine
    ports:
      - '3050:3050'
    volumes:
      - .:/home/<USER>/app
      - node_modules:/home/<USER>/app/node_modules
    working_dir: /home/<USER>/app/
    build:
      context: .
    depends_on:
      - mongo
    env_file:
      - .env

  mongo:
    image: mongo:latest
    ports:
      - '27017:27017'
    command:
      - --storageEngine=wiredTiger
    volumes:
      - data:/data/db
    logging:
      driver: none

  nginx:
    restart: always
    build:
      context: ./docker/nginx
    ports:
      - "80:80"

volumes:
  data:
  node_modules:
