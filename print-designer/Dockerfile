FROM node:18.8-alpine as base

FROM base as builder

WORKDIR /home/<USER>/app
COPY package*.json ./

COPY . .
RUN pwd
RUN yarn install
RUN yarn build

FROM base as runtime

ENV NODE_ENV=production
ENV PAYLOAD_CONFIG_PATH=dist/payload.config.ts

WORKDIR /home/<USER>/app
COPY package*.json  ./
COPY yarn.lock ./


RUN pwd
RUN yarn install --production
COPY --from=builder /home/<USER>/app/dist ./dist
COPY --from=builder /home/<USER>/app/build ./build

EXPOSE 3050

CMD ["node", "dist/server.js"]
