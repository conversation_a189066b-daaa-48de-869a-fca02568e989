{"name": "print-designer", "description": "A blank template to get started with Payload", "version": "1.0.0", "main": "dist/server.js", "license": "MIT", "scripts": {"dev": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts nodemon", "dev:app": "cd src/app && yarn serve", "install:app": "cd src/app && yarn install", "build:payload": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload build", "build:server": "tsc", "build:app": "cd src/app && yarn build", "build": "yarn build:app && yarn copyfiles && yarn build:payload && yarn build:server", "serve": "cross-env PAYLOAD_CONFIG_PATH=dist/payload.config.js NODE_ENV=production node dist/server.js", "copyfiles": "copyfiles -e 'src/app/**' -u 1 'src/**/*.{html,css,scss,ttf,woff,woff2,eot,svg,jpg,png,ico}' dist/ && copyfiles -u 1 'src/app/dist/**' dist/", "generate:types": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload generate:types", "generate:graphQLSchema": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload generate:graphQLSchema", "payload": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload"}, "dependencies": {"@payloadcms/bundler-webpack": "^1.0.0", "@payloadcms/db-mongodb": "^1.0.0", "@payloadcms/plugin-cloud": "^2.0.0", "@payloadcms/richtext-slate": "^1.0.0", "adm-zip": "^0.5.16", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^8.2.0", "express": "^4.17.1", "jszip": "^3.10.1", "nodemailer": "^6.9.7", "nunjucks": "^3.2.4", "payload": "^2.0.0"}, "devDependencies": {"@types/express": "^4.17.9", "copyfiles": "^2.4.1", "nodemon": "^2.0.6", "ts-node": "^9.1.1", "typescript": "^4.8.4"}, "resolutions": {"jackspeak": "2.1.1"}}