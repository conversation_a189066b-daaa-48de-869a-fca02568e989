{"name": "rtp-print-designer", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@quasar/extras": "^1.0.0", "axios": "^1.6.1", "core-js": "^3.8.3", "fabric": "^5.2.1", "quasar": "^2.0.0", "vue": "^3.2.13", "vue-i18n": "^9.6.5", "vuex": "^4.0.2", "webfontloader": "^1.6.28"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "1.33.0", "sass-loader": "^12.0.0", "vue-cli-plugin-quasar": "5.1.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}