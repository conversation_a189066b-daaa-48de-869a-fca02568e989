{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "strict": false, "esModuleInterop": true, "skipLibCheck": true, "outDir": "./dist", "rootDir": "./src", "jsx": "react", "paths": {"payload/generated-types": ["./src/payload-types.ts"]}}, "include": ["src"], "exclude": ["node_modules", "dist", "build", "src/app"], "ts-node": {"transpileOnly": true, "swc": true}}