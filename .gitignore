# Dependencies
node_modules/
vendor/

# Environment files
.env
.env.local
.env.production
.env.staging

# Build outputs
/frontend/dist/
/frontend/build/

# Laravel specific
/backend/storage/app/*
/backend/storage/framework/cache/*
/backend/storage/framework/sessions/*
/backend/storage/framework/views/*
/backend/storage/logs/*
/backend/bootstrap/cache/*
!/backend/storage/app/.gitkeep
!/backend/storage/app/public/.gitkeep
!/backend/storage/framework/.gitkeep
!/backend/storage/framework/cache/.gitkeep
!/backend/storage/framework/sessions/.gitkeep
!/backend/storage/framework/views/.gitkeep
!/backend/storage/logs/.gitkeep

# Database
/backend/database/database.sqlite
*.sqlite

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# External tools (has its own git repo)
print-designer/
