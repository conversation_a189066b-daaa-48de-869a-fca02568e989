#!/bin/bash

# Printily Deployment Script
# This script deploys the Printily platform using Docker

set -e

echo "🚀 Starting Printily Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p ssl
mkdir -p backend/storage/logs
mkdir -p backend/storage/app/public

# Set up environment files
print_status "Setting up environment files..."

# Backend environment
if [ ! -f backend/.env ]; then
    cp backend/.env.example backend/.env
    print_success "Created backend .env file"
else
    print_warning "Backend .env file already exists"
fi

# Frontend environment
if [ ! -f frontend/.env ]; then
    echo "VITE_API_URL=http://localhost:8000/api/v1" > frontend/.env
    echo "VITE_APP_NAME=Printily" >> frontend/.env
    print_success "Created frontend .env file"
else
    print_warning "Frontend .env file already exists"
fi

# Build and start containers
print_status "Building and starting Docker containers..."
docker-compose down --remove-orphans
docker-compose build --no-cache
docker-compose up -d

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 30

# Check if services are running
print_status "Checking service health..."

# Check backend
if curl -f http://localhost:8000/api/v1/products > /dev/null 2>&1; then
    print_success "Backend is running and responding"
else
    print_error "Backend is not responding"
fi

# Check frontend
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    print_success "Frontend is running and responding"
else
    print_error "Frontend is not responding"
fi

# Display deployment information
echo ""
echo "🎉 Printily Deployment Complete!"
echo ""
echo "📱 Access your application:"
echo "   Frontend: http://localhost:3000"
echo "   Backend API: http://localhost:8000"
echo "   Full App: http://localhost (via Nginx)"
echo ""
echo "🛠️ Management commands:"
echo "   View logs: docker-compose logs -f"
echo "   Stop: docker-compose down"
echo "   Restart: docker-compose restart"
echo "   Update: ./deploy.sh"
echo ""
echo "📊 Container status:"
docker-compose ps

print_success "Deployment completed successfully! 🚀"
