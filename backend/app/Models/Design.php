<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Design extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'user_id',
        'product_id',
        'design_file',
        'preview_image',
        'design_data',
        'is_public',
        'is_featured',
        'views',
        'likes',
    ];

    protected function casts(): array
    {
        return [
            'design_data' => 'array',
            'is_public' => 'boolean',
            'is_featured' => 'boolean',
        ];
    }

    /**
     * Get the user that owns the design.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the product that owns the design.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the order items for the design.
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Scope a query to only include public designs.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope a query to only include featured designs.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Increment the view count.
     */
    public function incrementViews()
    {
        $this->increment('views');
    }

    /**
     * Increment the like count.
     */
    public function incrementLikes()
    {
        $this->increment('likes');
    }
}
