<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Design;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DesignController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum')->except(['index', 'show', 'featured']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Design::with(['user', 'product'])->public();

        // Filter by product
        if ($request->has('product_id')) {
            $query->where('product_id', $request->product_id);
        }

        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Search by name
        if ($request->has('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['created_at', 'views', 'likes', 'name'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $perPage = min($request->get('per_page', 12), 50);
        $designs = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $designs
        ]);
    }

    /**
     * Get featured designs
     */
    public function featured(Request $request)
    {
        $designs = Design::with(['user', 'product'])
            ->featured()
            ->public()
            ->orderBy('created_at', 'desc')
            ->take($request->get('limit', 10))
            ->get();

        return response()->json([
            'success' => true,
            'data' => $designs
        ]);
    }

    /**
     * Get user's designs
     */
    public function myDesigns(Request $request)
    {
        $designs = Design::with('product')
            ->where('user_id', $request->user()->id)
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 12));

        return response()->json([
            'success' => true,
            'data' => $designs
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'product_id' => 'required|exists:products,id',
            'design_data' => 'required|array',
            'design_file' => 'required|file|image|max:10240', // 10MB max
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Store the design file
        $designFile = $request->file('design_file');
        $designPath = $designFile->store('designs', 'public');

        // Generate preview with mockup (this would be implemented with image processing)
        $previewPath = $this->generatePreview($request->product_id, $designPath);

        $design = Design::create([
            'name' => $request->name,
            'user_id' => $request->user()->id,
            'product_id' => $request->product_id,
            'design_file' => $designPath,
            'preview_image' => $previewPath,
            'design_data' => $request->design_data,
            'is_public' => $request->get('is_public', false),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Design saved successfully',
            'data' => $design->load(['user', 'product'])
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $design = Design::with(['user', 'product'])->findOrFail($id);

        // Increment view count
        $design->incrementViews();

        return response()->json([
            'success' => true,
            'data' => $design
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $design = Design::findOrFail($id);

        // Check if user owns the design
        if ($design->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'design_data' => 'sometimes|array',
            'design_file' => 'sometimes|file|image|max:10240',
            'is_public' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $updateData = $request->only(['name', 'design_data', 'is_public']);

        // Handle file update
        if ($request->hasFile('design_file')) {
            // Delete old file
            Storage::disk('public')->delete($design->design_file);

            // Store new file
            $designFile = $request->file('design_file');
            $designPath = $designFile->store('designs', 'public');
            $updateData['design_file'] = $designPath;

            // Generate new preview
            $updateData['preview_image'] = $this->generatePreview($design->product_id, $designPath);
        }

        $design->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Design updated successfully',
            'data' => $design->load(['user', 'product'])
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, $id)
    {
        $design = Design::findOrFail($id);

        // Check if user owns the design or is admin
        if ($design->user_id !== $request->user()->id && !$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Delete files
        Storage::disk('public')->delete([$design->design_file, $design->preview_image]);

        $design->delete();

        return response()->json([
            'success' => true,
            'message' => 'Design deleted successfully'
        ]);
    }

    /**
     * Generate preview image with mockup
     */
    private function generatePreview($productId, $designPath)
    {
        // This is a placeholder - in real implementation, you would:
        // 1. Load the product mockup image
        // 2. Load the design image
        // 3. Overlay the design on the mockup using the mockup_positions
        // 4. Save the result as preview image

        // For now, just copy the design file as preview
        $previewPath = 'previews/' . Str::uuid() . '.png';
        Storage::disk('public')->copy($designPath, $previewPath);

        return $previewPath;
    }
}
