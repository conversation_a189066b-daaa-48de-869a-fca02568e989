<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class MockDataController extends Controller
{
    /**
     * Get mock categories
     */
    public function categories()
    {
        $categories = [
            [
                'id' => 1,
                'name' => 'T-Shirts',
                'slug' => 't-shirts',
                'description' => 'Comfortable cotton t-shirts for custom designs',
                'image' => null,
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 2,
                'name' => 'Mugs',
                'slug' => 'mugs',
                'description' => 'Ceramic mugs perfect for personalized designs',
                'image' => null,
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 3,
                'name' => 'Hoodies',
                'slug' => 'hoodies',
                'description' => 'Warm hoodies perfect for custom prints',
                'image' => null,
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 4,
                'name' => 'Accessories',
                'slug' => 'accessories',
                'description' => 'Custom accessories and home items',
                'image' => null,
                'is_active' => true,
                'sort_order' => 4,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * Get mock products
     */
    public function products(Request $request)
    {
        $products = [
            [
                'id' => 1,
                'name' => 'Classic Cotton T-Shirt',
                'slug' => 'classic-cotton-t-shirt',
                'description' => 'Comfortable 100% cotton t-shirt perfect for custom designs. High-quality fabric that feels great and lasts long.',
                'base_price' => 1500.00,
                'category_id' => 1,
                'category' => [
                    'id' => 1,
                    'name' => 'T-Shirts',
                    'slug' => 't-shirts'
                ],
                'mockup_image' => 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop&crop=center',
                'mockup_positions' => [
                    'front' => ['x' => 150, 'y' => 100, 'width' => 200, 'height' => 250],
                    'back' => ['x' => 150, 'y' => 100, 'width' => 200, 'height' => 250]
                ],
                'available_sizes' => ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
                'available_colors' => [
                    ['name' => 'White', 'hex' => '#FFFFFF'],
                    ['name' => 'Black', 'hex' => '#000000'],
                    ['name' => 'Navy', 'hex' => '#001f3f'],
                    ['name' => 'Red', 'hex' => '#FF4136'],
                    ['name' => 'Gray', 'hex' => '#808080']
                ],
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 2,
                'name' => 'Premium Coffee Mug',
                'slug' => 'premium-coffee-mug',
                'description' => '11oz ceramic coffee mug perfect for morning coffee and custom designs. Dishwasher and microwave safe.',
                'base_price' => 800.00,
                'category_id' => 2,
                'category' => [
                    'id' => 2,
                    'name' => 'Mugs',
                    'slug' => 'mugs'
                ],
                'mockup_image' => 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=500&h=600&fit=crop&crop=center',
                'mockup_positions' => [
                    'wrap' => ['x' => 50, 'y' => 80, 'width' => 250, 'height' => 120]
                ],
                'available_sizes' => ['11oz'],
                'available_colors' => [
                    ['name' => 'White', 'hex' => '#FFFFFF'],
                    ['name' => 'Black', 'hex' => '#000000'],
                    ['name' => 'Blue', 'hex' => '#0074D9']
                ],
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 3,
                'name' => 'Cozy Pullover Hoodie',
                'slug' => 'cozy-pullover-hoodie',
                'description' => 'Warm and comfortable pullover hoodie with kangaroo pocket. Perfect for custom designs and cold weather.',
                'base_price' => 4500.00,
                'category_id' => 3,
                'category' => [
                    'id' => 3,
                    'name' => 'Hoodies',
                    'slug' => 'hoodies'
                ],
                'mockup_image' => 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=500&h=600&fit=crop&crop=center',
                'mockup_positions' => [
                    'front' => ['x' => 150, 'y' => 120, 'width' => 180, 'height' => 220],
                    'back' => ['x' => 150, 'y' => 120, 'width' => 180, 'height' => 220]
                ],
                'available_sizes' => ['S', 'M', 'L', 'XL', 'XXL'],
                'available_colors' => [
                    ['name' => 'Black', 'hex' => '#000000'],
                    ['name' => 'Gray', 'hex' => '#808080'],
                    ['name' => 'Navy', 'hex' => '#001f3f'],
                    ['name' => 'Maroon', 'hex' => '#85144b']
                ],
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 4,
                'name' => 'Vintage Style T-Shirt',
                'slug' => 'vintage-style-t-shirt',
                'description' => 'Soft vintage-style t-shirt with a relaxed fit. Perfect for retro designs and casual wear.',
                'base_price' => 1800.00,
                'category_id' => 1,
                'category' => [
                    'id' => 1,
                    'name' => 'T-Shirts',
                    'slug' => 't-shirts'
                ],
                'mockup_image' => 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=500&h=600&fit=crop&crop=center',
                'mockup_positions' => [
                    'front' => ['x' => 150, 'y' => 100, 'width' => 200, 'height' => 250],
                    'back' => ['x' => 150, 'y' => 100, 'width' => 200, 'height' => 250]
                ],
                'available_sizes' => ['S', 'M', 'L', 'XL'],
                'available_colors' => [
                    ['name' => 'Heather Gray', 'hex' => '#D3D3D3'],
                    ['name' => 'Vintage White', 'hex' => '#F5F5DC'],
                    ['name' => 'Faded Black', 'hex' => '#36454F']
                ],
                'is_active' => true,
                'sort_order' => 4,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 5,
                'name' => 'Travel Mug',
                'slug' => 'travel-mug',
                'description' => '15oz stainless steel travel mug with lid. Perfect for on-the-go coffee and custom designs.',
                'base_price' => 1200.00,
                'category_id' => 2,
                'category' => [
                    'id' => 2,
                    'name' => 'Mugs',
                    'slug' => 'mugs'
                ],
                'mockup_image' => 'https://images.unsplash.com/photo-1514228742587-6b1558fcf93a?w=500&h=600&fit=crop&crop=center',
                'mockup_positions' => [
                    'wrap' => ['x' => 40, 'y' => 60, 'width' => 280, 'height' => 140]
                ],
                'available_sizes' => ['15oz'],
                'available_colors' => [
                    ['name' => 'Silver', 'hex' => '#C0C0C0'],
                    ['name' => 'Black', 'hex' => '#000000'],
                    ['name' => 'Blue', 'hex' => '#0074D9']
                ],
                'is_active' => true,
                'sort_order' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 6,
                'name' => 'Zip-Up Hoodie',
                'slug' => 'zip-up-hoodie',
                'description' => 'Premium zip-up hoodie with front pockets. Perfect for layering and custom designs.',
                'base_price' => 5200.00,
                'category_id' => 3,
                'category' => [
                    'id' => 3,
                    'name' => 'Hoodies',
                    'slug' => 'hoodies'
                ],
                'mockup_image' => 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500&h=600&fit=crop&crop=center',
                'mockup_positions' => [
                    'front' => ['x' => 140, 'y' => 110, 'width' => 190, 'height' => 240],
                    'back' => ['x' => 140, 'y' => 110, 'width' => 190, 'height' => 240]
                ],
                'available_sizes' => ['S', 'M', 'L', 'XL', 'XXL'],
                'available_colors' => [
                    ['name' => 'Charcoal', 'hex' => '#36454F'],
                    ['name' => 'Forest Green', 'hex' => '#228B22'],
                    ['name' => 'Burgundy', 'hex' => '#800020'],
                    ['name' => 'Navy Blue', 'hex' => '#000080']
                ],
                'is_active' => true,
                'sort_order' => 6,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 7,
                'name' => 'Tank Top',
                'slug' => 'tank-top',
                'description' => 'Lightweight cotton tank top perfect for summer and gym wear. Great for bold designs.',
                'base_price' => 1200.00,
                'category_id' => 1,
                'category' => [
                    'id' => 1,
                    'name' => 'T-Shirts',
                    'slug' => 't-shirts'
                ],
                'mockup_image' => 'https://images.unsplash.com/photo-1618354691373-d851c5c3a990?w=500&h=600&fit=crop&crop=center',
                'mockup_positions' => [
                    'front' => ['x' => 160, 'y' => 90, 'width' => 180, 'height' => 220],
                    'back' => ['x' => 160, 'y' => 90, 'width' => 180, 'height' => 220]
                ],
                'available_sizes' => ['XS', 'S', 'M', 'L', 'XL'],
                'available_colors' => [
                    ['name' => 'White', 'hex' => '#FFFFFF'],
                    ['name' => 'Black', 'hex' => '#000000'],
                    ['name' => 'Gray', 'hex' => '#808080'],
                    ['name' => 'Navy', 'hex' => '#001f3f'],
                    ['name' => 'Red', 'hex' => '#FF4136']
                ],
                'is_active' => true,
                'sort_order' => 7,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 8,
                'name' => 'Long Sleeve Shirt',
                'slug' => 'long-sleeve-shirt',
                'description' => 'Comfortable long sleeve cotton shirt. Perfect for cooler weather and sleeve designs.',
                'base_price' => 2200.00,
                'category_id' => 1,
                'category' => [
                    'id' => 1,
                    'name' => 'T-Shirts',
                    'slug' => 't-shirts'
                ],
                'mockup_image' => 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=500&h=600&fit=crop&crop=center',
                'mockup_positions' => [
                    'front' => ['x' => 150, 'y' => 100, 'width' => 200, 'height' => 250],
                    'back' => ['x' => 150, 'y' => 100, 'width' => 200, 'height' => 250],
                    'left_sleeve' => ['x' => 50, 'y' => 150, 'width' => 80, 'height' => 120],
                    'right_sleeve' => ['x' => 370, 'y' => 150, 'width' => 80, 'height' => 120]
                ],
                'available_sizes' => ['S', 'M', 'L', 'XL', 'XXL'],
                'available_colors' => [
                    ['name' => 'White', 'hex' => '#FFFFFF'],
                    ['name' => 'Black', 'hex' => '#000000'],
                    ['name' => 'Heather Gray', 'hex' => '#D3D3D3'],
                    ['name' => 'Olive', 'hex' => '#808000']
                ],
                'is_active' => true,
                'sort_order' => 8,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 9,
                'name' => 'Ceramic Plate',
                'slug' => 'ceramic-plate',
                'description' => '10-inch ceramic dinner plate. Perfect for custom designs and special occasions.',
                'base_price' => 1800.00,
                'category_id' => 2,
                'category' => [
                    'id' => 2,
                    'name' => 'Mugs',
                    'slug' => 'mugs'
                ],
                'mockup_image' => 'https://images.unsplash.com/photo-1578749556568-bc2c40e68b61?w=500&h=600&fit=crop&crop=center',
                'mockup_positions' => [
                    'center' => ['x' => 100, 'y' => 100, 'width' => 300, 'height' => 300]
                ],
                'available_sizes' => ['10 inch'],
                'available_colors' => [
                    ['name' => 'White', 'hex' => '#FFFFFF'],
                    ['name' => 'Cream', 'hex' => '#F5F5DC'],
                    ['name' => 'Light Blue', 'hex' => '#ADD8E6']
                ],
                'is_active' => true,
                'sort_order' => 9,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 10,
                'name' => 'Crewneck Sweatshirt',
                'slug' => 'crewneck-sweatshirt',
                'description' => 'Classic crewneck sweatshirt with ribbed cuffs. Comfortable and perfect for custom prints.',
                'base_price' => 3800.00,
                'category_id' => 3,
                'category' => [
                    'id' => 3,
                    'name' => 'Hoodies',
                    'slug' => 'hoodies'
                ],
                'mockup_image' => 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=500&h=600&fit=crop&crop=center',
                'mockup_positions' => [
                    'front' => ['x' => 150, 'y' => 120, 'width' => 200, 'height' => 240],
                    'back' => ['x' => 150, 'y' => 120, 'width' => 200, 'height' => 240]
                ],
                'available_sizes' => ['S', 'M', 'L', 'XL', 'XXL'],
                'available_colors' => [
                    ['name' => 'Ash Gray', 'hex' => '#B2BEB5'],
                    ['name' => 'Navy', 'hex' => '#000080'],
                    ['name' => 'Forest Green', 'hex' => '#228B22'],
                    ['name' => 'Maroon', 'hex' => '#800000'],
                    ['name' => 'Black', 'hex' => '#000000']
                ],
                'is_active' => true,
                'sort_order' => 10,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ];

        // Apply filters
        if ($request->has('category_id')) {
            $products = array_filter($products, function($product) use ($request) {
                return $product['category_id'] == $request->category_id;
            });
        }

        if ($request->has('search')) {
            $search = strtolower($request->search);
            $products = array_filter($products, function($product) use ($search) {
                return strpos(strtolower($product['name']), $search) !== false ||
                       strpos(strtolower($product['description']), $search) !== false;
            });
        }

        // Pagination simulation
        $perPage = $request->get('per_page', 12);
        $page = $request->get('page', 1);
        $total = count($products);
        $products = array_slice($products, ($page - 1) * $perPage, $perPage);

        return response()->json([
            'success' => true,
            'data' => [
                'data' => array_values($products),
                'current_page' => (int)$page,
                'last_page' => ceil($total / $perPage),
                'per_page' => (int)$perPage,
                'total' => $total,
            ]
        ]);
    }

    /**
     * Get single product
     */
    public function product($id)
    {
        // Get all products from the products method
        $productsResponse = $this->products(request());
        $allProducts = json_decode($productsResponse->getContent(), true)['data']['data'];

        $product = null;
        foreach ($allProducts as $p) {
            if ($p['id'] == $id) {
                $product = $p;
                break;
            }
        }

        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $product
        ]);
    }

    /**
     * Get mock featured designs
     */
    public function featuredDesigns()
    {
        $designs = [
            [
                'id' => 1,
                'name' => 'Cool Typography Design',
                'user_id' => 1,
                'product_id' => 1,
                'design_file' => '/designs/design1.png',
                'preview_image' => '/previews/preview1.png',
                'is_public' => true,
                'is_featured' => true,
                'views' => 245,
                'likes' => 18,
                'user' => ['id' => 1, 'name' => 'John Designer'],
                'product' => ['id' => 1, 'name' => 'Classic Cotton T-Shirt'],
                'created_at' => now()->subDays(5),
            ],
            [
                'id' => 2,
                'name' => 'Minimalist Coffee Quote',
                'user_id' => 2,
                'product_id' => 2,
                'design_file' => '/designs/design2.png',
                'preview_image' => '/previews/preview2.png',
                'is_public' => true,
                'is_featured' => true,
                'views' => 189,
                'likes' => 24,
                'user' => ['id' => 2, 'name' => 'Sarah Creative'],
                'product' => ['id' => 2, 'name' => 'Premium Coffee Mug'],
                'created_at' => now()->subDays(3),
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $designs
        ]);
    }
}
