<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AdminProductController extends Controller
{
    /**
     * Get all products for admin
     */
    public function index(Request $request)
    {
        // Get products from MockDataController
        $mockController = new MockDataController();
        $productsResponse = $mockController->products($request);
        $productsData = json_decode($productsResponse->getContent(), true);

        // Add admin-specific data
        $products = $productsData['data']['data'];
        foreach ($products as &$product) {
            $product['admin_data'] = [
                'total_sales' => rand(10, 100),
                'revenue' => $product['base_price'] * rand(10, 100),
                'stock_status' => rand(0, 1) ? 'in_stock' : 'low_stock',
                'created_by' => 'Admin User',
                'last_updated' => now()->subDays(rand(1, 30))->toISOString()
            ];
        }

        return response()->json([
            'success' => true,
            'data' => [
                'products' => $products,
                'pagination' => $productsData['data'],
                'summary' => [
                    'total_products' => count($products),
                    'active_products' => count(array_filter($products, fn($p) => $p['is_active'])),
                    'total_revenue' => array_sum(array_column(array_column($products, 'admin_data'), 'revenue'))
                ]
            ]
        ]);
    }

    /**
     * Create new product
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'base_price' => 'required|numeric|min:0',
            'category_id' => 'required|integer',
            'available_sizes' => 'required|array',
            'available_colors' => 'required|array',
            'mockup_image' => 'nullable|url',
            'mockup_positions' => 'required|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Mock product creation
        $product = [
            'id' => rand(100, 999),
            'name' => $request->name,
            'slug' => \Str::slug($request->name),
            'description' => $request->description,
            'base_price' => $request->base_price,
            'category_id' => $request->category_id,
            'mockup_image' => $request->mockup_image ?: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop',
            'mockup_positions' => $request->mockup_positions,
            'available_sizes' => $request->available_sizes,
            'available_colors' => $request->available_colors,
            'is_active' => true,
            'sort_order' => 999,
            'created_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
            'admin_data' => [
                'total_sales' => 0,
                'revenue' => 0,
                'stock_status' => 'in_stock',
                'created_by' => 'Admin User',
                'last_updated' => now()->toISOString()
            ]
        ];

        return response()->json([
            'success' => true,
            'message' => 'Product created successfully',
            'data' => $product
        ], 201);
    }

    /**
     * Update product
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string',
            'base_price' => 'sometimes|required|numeric|min:0',
            'category_id' => 'sometimes|required|integer',
            'available_sizes' => 'sometimes|required|array',
            'available_colors' => 'sometimes|required|array',
            'mockup_image' => 'sometimes|nullable|url',
            'mockup_positions' => 'sometimes|required|array',
            'is_active' => 'sometimes|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Mock product update
        $product = [
            'id' => $id,
            'name' => $request->name ?? 'Updated Product',
            'slug' => \Str::slug($request->name ?? 'updated-product'),
            'description' => $request->description ?? 'Updated description',
            'base_price' => $request->base_price ?? 1500.00,
            'category_id' => $request->category_id ?? 1,
            'mockup_image' => $request->mockup_image ?: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop',
            'mockup_positions' => $request->mockup_positions ?? ['front' => ['x' => 150, 'y' => 100, 'width' => 200, 'height' => 250]],
            'available_sizes' => $request->available_sizes ?? ['S', 'M', 'L', 'XL'],
            'available_colors' => $request->available_colors ?? [['name' => 'White', 'hex' => '#FFFFFF']],
            'is_active' => $request->is_active ?? true,
            'sort_order' => 1,
            'created_at' => now()->subDays(30)->toISOString(),
            'updated_at' => now()->toISOString(),
            'admin_data' => [
                'total_sales' => rand(10, 100),
                'revenue' => ($request->base_price ?? 1500.00) * rand(10, 100),
                'stock_status' => 'in_stock',
                'created_by' => 'Admin User',
                'last_updated' => now()->toISOString()
            ]
        ];

        return response()->json([
            'success' => true,
            'message' => 'Product updated successfully',
            'data' => $product
        ]);
    }

    /**
     * Delete product
     */
    public function destroy($id)
    {
        // Mock product deletion
        return response()->json([
            'success' => true,
            'message' => 'Product deleted successfully'
        ]);
    }

    /**
     * Toggle product status
     */
    public function toggleStatus($id)
    {
        // Mock status toggle
        $isActive = rand(0, 1);
        
        return response()->json([
            'success' => true,
            'message' => 'Product status updated successfully',
            'data' => [
                'id' => $id,
                'is_active' => $isActive,
                'status' => $isActive ? 'active' : 'inactive'
            ]
        ]);
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,delete',
            'product_ids' => 'required|array|min:1',
            'product_ids.*' => 'integer'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $action = $request->action;
        $productIds = $request->product_ids;
        $count = count($productIds);

        $messages = [
            'activate' => "Successfully activated {$count} products",
            'deactivate' => "Successfully deactivated {$count} products",
            'delete' => "Successfully deleted {$count} products"
        ];

        return response()->json([
            'success' => true,
            'message' => $messages[$action],
            'data' => [
                'action' => $action,
                'affected_products' => $count,
                'product_ids' => $productIds
            ]
        ]);
    }
}
