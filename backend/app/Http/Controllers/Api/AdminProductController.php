<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AdminProductController extends Controller
{
    /**
     * Get all products for admin
     */
    public function index(Request $request)
    {
        // Get products from MockDataController
        $mockController = new MockDataController();
        $productsResponse = $mockController->products($request);
        $productsData = json_decode($productsResponse->getContent(), true);

        // Add realistic admin-specific data
        $products = $productsData['data']['data'];
        foreach ($products as &$product) {
            // Generate realistic sales data based on product characteristics
            $basePrice = $product['base_price'];
            $isPopular = in_array($product['id'], [1, 2, 3]); // First 3 products are popular
            $isPremium = $basePrice > 3000;

            // Calculate realistic sales numbers
            $salesMultiplier = $isPopular ? rand(150, 300) / 100 : rand(50, 150) / 100;
            if ($isPremium) $salesMultiplier *= 0.6; // Premium items sell less

            $totalSales = max(1, intval(30 * $salesMultiplier)); // Base 30 sales
            $revenue = $totalSales * $basePrice;
            $views = $totalSales * rand(20, 40); // 20-40 views per sale
            $conversionRate = $views > 0 ? round(($totalSales / $views) * 100, 1) : 0;

            // Stock status based on sales performance
            $stockStatus = 'in_stock';
            if ($totalSales > 80) $stockStatus = 'low_stock';
            if ($totalSales > 150) $stockStatus = 'high_demand';

            $product['admin_data'] = [
                'total_sales' => $totalSales,
                'revenue' => $revenue,
                'stock_status' => $stockStatus,
                'created_by' => 'Admin User',
                'last_updated' => now()->subDays(rand(1, 7))->toISOString(),
                'views' => $views,
                'conversion_rate' => $conversionRate,
                'profit_margin' => rand(45, 75), // 45-75% profit margin
                'monthly_sales' => intval($totalSales * 0.3), // 30% of total sales this month
                'weekly_sales' => intval($totalSales * 0.1), // 10% of total sales this week
                'daily_sales' => rand(0, 3), // 0-3 sales today
                'inventory_count' => rand(50, 500), // Stock count
                'reorder_point' => 20,
                'supplier_cost' => intval($basePrice * (rand(25, 55) / 100)), // 25-55% of selling price
                'last_sale_date' => now()->subDays(rand(0, 5))->toISOString()
            ];
        }

        return response()->json([
            'success' => true,
            'data' => [
                'products' => $products,
                'pagination' => $productsData['data'],
                'summary' => [
                    'total_products' => count($products),
                    'active_products' => count(array_filter($products, fn($p) => $p['is_active'])),
                    'total_revenue' => array_sum(array_column(array_column($products, 'admin_data'), 'revenue'))
                ]
            ]
        ]);
    }

    /**
     * Create new product
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'base_price' => 'required|numeric|min:0',
            'category_id' => 'required|integer',
            'available_sizes' => 'required|array',
            'available_colors' => 'required|array',
            'mockup_image' => 'nullable|url',
            'mockup_positions' => 'required|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Mock product creation
        $product = [
            'id' => rand(100, 999),
            'name' => $request->name,
            'slug' => \Str::slug($request->name),
            'description' => $request->description,
            'base_price' => $request->base_price,
            'category_id' => $request->category_id,
            'mockup_image' => $request->mockup_image ?: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop',
            'mockup_positions' => $request->mockup_positions,
            'available_sizes' => $request->available_sizes,
            'available_colors' => $request->available_colors,
            'is_active' => true,
            'sort_order' => 999,
            'created_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
            'admin_data' => [
                'total_sales' => 0,
                'revenue' => 0,
                'stock_status' => 'in_stock',
                'created_by' => 'Admin User',
                'last_updated' => now()->toISOString()
            ]
        ];

        return response()->json([
            'success' => true,
            'message' => 'Product created successfully',
            'data' => $product
        ], 201);
    }

    /**
     * Update product
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string',
            'base_price' => 'sometimes|required|numeric|min:0',
            'category_id' => 'sometimes|required|integer',
            'available_sizes' => 'sometimes|required|array',
            'available_colors' => 'sometimes|required|array',
            'mockup_image' => 'sometimes|nullable|url',
            'mockup_positions' => 'sometimes|required|array',
            'is_active' => 'sometimes|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Mock product update
        $product = [
            'id' => $id,
            'name' => $request->name ?? 'Updated Product',
            'slug' => \Str::slug($request->name ?? 'updated-product'),
            'description' => $request->description ?? 'Updated description',
            'base_price' => $request->base_price ?? 1500.00,
            'category_id' => $request->category_id ?? 1,
            'mockup_image' => $request->mockup_image ?: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop',
            'mockup_positions' => $request->mockup_positions ?? ['front' => ['x' => 150, 'y' => 100, 'width' => 200, 'height' => 250]],
            'available_sizes' => $request->available_sizes ?? ['S', 'M', 'L', 'XL'],
            'available_colors' => $request->available_colors ?? [['name' => 'White', 'hex' => '#FFFFFF']],
            'is_active' => $request->is_active ?? true,
            'sort_order' => 1,
            'created_at' => now()->subDays(30)->toISOString(),
            'updated_at' => now()->toISOString(),
            'admin_data' => [
                'total_sales' => rand(10, 100),
                'revenue' => ($request->base_price ?? 1500.00) * rand(10, 100),
                'stock_status' => 'in_stock',
                'created_by' => 'Admin User',
                'last_updated' => now()->toISOString()
            ]
        ];

        return response()->json([
            'success' => true,
            'message' => 'Product updated successfully',
            'data' => $product
        ]);
    }

    /**
     * Delete product
     */
    public function destroy($id)
    {
        // Mock product deletion
        return response()->json([
            'success' => true,
            'message' => 'Product deleted successfully'
        ]);
    }

    /**
     * Toggle product status
     */
    public function toggleStatus($id)
    {
        // Mock status toggle
        $isActive = rand(0, 1);
        
        return response()->json([
            'success' => true,
            'message' => 'Product status updated successfully',
            'data' => [
                'id' => $id,
                'is_active' => $isActive,
                'status' => $isActive ? 'active' : 'inactive'
            ]
        ]);
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,delete',
            'product_ids' => 'required|array|min:1',
            'product_ids.*' => 'integer'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $action = $request->action;
        $productIds = $request->product_ids;
        $count = count($productIds);

        $messages = [
            'activate' => "Successfully activated {$count} products",
            'deactivate' => "Successfully deactivated {$count} products",
            'delete' => "Successfully deleted {$count} products"
        ];

        return response()->json([
            'success' => true,
            'message' => $messages[$action],
            'data' => [
                'action' => $action,
                'affected_products' => $count,
                'product_ids' => $productIds
            ]
        ]);
    }
}
