<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;

class AdminAuthController extends Controller
{
    /**
     * Admin login
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Mock admin credentials for demo
        $adminCredentials = [
            '<EMAIL>' => 'admin123',
            '<EMAIL>' => 'nadjib123',
            '<EMAIL>' => 'manager123'
        ];

        $email = $request->email;
        $password = $request->password;

        // Check if admin credentials are valid
        if (!isset($adminCredentials[$email]) || $adminCredentials[$email] !== $password) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid admin credentials'
            ], 401);
        }

        // Create admin user object
        $admin = [
            'id' => 1,
            'name' => $this->getAdminName($email),
            'email' => $email,
            'role' => 'admin',
            'permissions' => [
                'products.view',
                'products.create',
                'products.edit',
                'products.delete',
                'orders.view',
                'orders.manage',
                'users.view',
                'users.manage',
                'designs.view',
                'designs.moderate',
                'analytics.view',
                'settings.manage'
            ],
            'avatar' => 'https://ui-avatars.com/api/?name=' . urlencode($this->getAdminName($email)) . '&background=ef4444&color=fff',
            'last_login' => now()->toISOString(),
            'created_at' => now()->subMonths(6)->toISOString(),
            'updated_at' => now()->toISOString(),
        ];

        // Mock admin token
        $token = 'admin_token_' . base64_encode($email . '_' . time() . '_admin');

        return response()->json([
            'success' => true,
            'message' => 'Admin login successful',
            'data' => [
                'admin' => $admin,
                'token' => $token,
                'token_type' => 'Bearer',
                'expires_in' => 3600 * 24 // 24 hours
            ]
        ]);
    }

    /**
     * Get admin profile
     */
    public function profile(Request $request)
    {
        // Mock admin profile
        $admin = [
            'id' => 1,
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'permissions' => [
                'products.view',
                'products.create',
                'products.edit',
                'products.delete',
                'orders.view',
                'orders.manage',
                'users.view',
                'users.manage',
                'designs.view',
                'designs.moderate',
                'analytics.view',
                'settings.manage'
            ],
            'avatar' => 'https://ui-avatars.com/api/?name=Admin+User&background=ef4444&color=fff',
            'last_login' => now()->toISOString(),
            'created_at' => now()->subMonths(6)->toISOString(),
            'updated_at' => now()->toISOString(),
        ];

        return response()->json([
            'success' => true,
            'data' => $admin
        ]);
    }

    /**
     * Admin logout
     */
    public function logout(Request $request)
    {
        return response()->json([
            'success' => true,
            'message' => 'Admin logged out successfully'
        ]);
    }

    /**
     * Get dashboard statistics
     */
    public function dashboardStats(Request $request)
    {
        $stats = [
            'overview' => [
                'total_products' => 10,
                'total_orders' => 156,
                'total_users' => 1247,
                'total_designs' => 89,
                'revenue_today' => 45600.00,
                'revenue_month' => 1234500.00,
                'orders_pending' => 23,
                'orders_processing' => 12
            ],
            'recent_orders' => [
                [
                    'id' => 'ORD-001',
                    'customer' => 'Ahmed Benali',
                    'product' => 'Classic Cotton T-Shirt',
                    'amount' => 1500.00,
                    'status' => 'pending',
                    'created_at' => now()->subHours(2)->toISOString()
                ],
                [
                    'id' => 'ORD-002',
                    'customer' => 'Fatima Khelil',
                    'product' => 'Premium Coffee Mug',
                    'amount' => 800.00,
                    'status' => 'processing',
                    'created_at' => now()->subHours(4)->toISOString()
                ],
                [
                    'id' => 'ORD-003',
                    'customer' => 'Youcef Mammeri',
                    'product' => 'Zip-Up Hoodie',
                    'amount' => 5200.00,
                    'status' => 'completed',
                    'created_at' => now()->subHours(6)->toISOString()
                ]
            ],
            'top_products' => [
                [
                    'id' => 1,
                    'name' => 'Classic Cotton T-Shirt',
                    'sales' => 45,
                    'revenue' => 67500.00,
                    'image' => 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=100&h=100&fit=crop'
                ],
                [
                    'id' => 2,
                    'name' => 'Premium Coffee Mug',
                    'sales' => 32,
                    'revenue' => 25600.00,
                    'image' => 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=100&h=100&fit=crop'
                ],
                [
                    'id' => 3,
                    'name' => 'Cozy Pullover Hoodie',
                    'sales' => 18,
                    'revenue' => 81000.00,
                    'image' => 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=100&h=100&fit=crop'
                ]
            ],
            'sales_chart' => [
                ['date' => now()->subDays(6)->format('Y-m-d'), 'sales' => 12500],
                ['date' => now()->subDays(5)->format('Y-m-d'), 'sales' => 18200],
                ['date' => now()->subDays(4)->format('Y-m-d'), 'sales' => 15800],
                ['date' => now()->subDays(3)->format('Y-m-d'), 'sales' => 22100],
                ['date' => now()->subDays(2)->format('Y-m-d'), 'sales' => 19500],
                ['date' => now()->subDays(1)->format('Y-m-d'), 'sales' => 25300],
                ['date' => now()->format('Y-m-d'), 'sales' => 28900]
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get admin name from email
     */
    private function getAdminName($email)
    {
        $names = [
            '<EMAIL>' => 'Admin User',
            '<EMAIL>' => 'Nadjib Dabouz',
            '<EMAIL>' => 'Store Manager'
        ];

        return $names[$email] ?? 'Admin User';
    }
}
