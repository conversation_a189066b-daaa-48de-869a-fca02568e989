<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class MockAuthController extends Controller
{
    /**
     * Register a new user (mock)
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Mock user creation (no database)
        $user = [
            'id' => rand(1000, 9999),
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'email_verified_at' => null,
            'role' => 'user',
            'created_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ];

        // Mock token
        $token = 'mock_token_' . base64_encode($request->email . '_' . time());

        return response()->json([
            'success' => true,
            'message' => 'User registered successfully',
            'data' => [
                'user' => $user,
                'token' => $token,
                'token_type' => 'Bearer'
            ]
        ], 201);
    }

    /**
     * Login user (mock)
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Mock login - always successful for demo
        $user = [
            'id' => 1,
            'name' => 'Demo User',
            'email' => $request->email,
            'phone' => '+213 555 123 456',
            'email_verified_at' => now()->toISOString(),
            'role' => 'user',
            'created_at' => now()->subDays(30)->toISOString(),
            'updated_at' => now()->toISOString(),
        ];

        // Mock token
        $token = 'mock_token_' . base64_encode($request->email . '_' . time());

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => $user,
                'token' => $token,
                'token_type' => 'Bearer'
            ]
        ]);
    }

    /**
     * Logout user (mock)
     */
    public function logout(Request $request)
    {
        // Mock logout - always successful
        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }

    /**
     * Get user profile (mock)
     */
    public function profile(Request $request)
    {
        // Mock user profile
        $user = [
            'id' => 1,
            'name' => 'Demo User',
            'email' => '<EMAIL>',
            'phone' => '+213 555 123 456',
            'email_verified_at' => now()->toISOString(),
            'role' => 'user',
            'created_at' => now()->subDays(30)->toISOString(),
            'updated_at' => now()->toISOString(),
        ];

        return response()->json([
            'success' => true,
            'data' => $user
        ]);
    }

    /**
     * Update user profile (mock)
     */
    public function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Mock profile update
        $user = [
            'id' => 1,
            'name' => $request->name,
            'email' => '<EMAIL>',
            'phone' => $request->phone,
            'email_verified_at' => now()->toISOString(),
            'role' => 'user',
            'created_at' => now()->subDays(30)->toISOString(),
            'updated_at' => now()->toISOString(),
        ];

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => $user
        ]);
    }

    /**
     * Change password (mock)
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        // Mock password change - always successful
        return response()->json([
            'success' => true,
            'message' => 'Password changed successfully'
        ]);
    }
}
