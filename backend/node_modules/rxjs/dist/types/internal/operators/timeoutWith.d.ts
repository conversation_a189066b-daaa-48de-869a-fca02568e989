import { ObservableInput, OperatorFunction, SchedulerLike } from '../types';
/** @deprecated Replaced with {@link timeout}. Instead of `timeoutWith(someDate, a$, scheduler)`, use the configuration object
 * `timeout({ first: someDate, with: () => a$, scheduler })`. Will be removed in v8. */
export declare function timeoutWith<T, R>(dueBy: Date, switchTo: ObservableInput<R>, scheduler?: SchedulerLike): OperatorFunction<T, T | R>;
/** @deprecated Replaced with {@link timeout}. Instead of `timeoutWith(100, a$, scheduler)`, use the configuration object
 *  `timeout({ each: 100, with: () => a$, scheduler })`. Will be removed in v8. */
export declare function timeoutWith<T, R>(waitFor: number, switchTo: ObservableInput<R>, scheduler?: SchedulerLike): OperatorFunction<T, T | R>;
//# sourceMappingURL=timeoutWith.d.ts.map