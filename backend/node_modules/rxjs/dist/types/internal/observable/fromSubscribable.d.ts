import { Observable } from '../Observable';
import { Subscribable } from '../types';
/**
 * Used to convert a subscribable to an observable.
 *
 * Currently, this is only used within internals.
 *
 * TODO: Discuss ObservableInput supporting "Subscribable".
 * https://github.com/ReactiveX/rxjs/issues/5909
 *
 * @param subscribable A subscribable
 */
export declare function fromSubscribable<T>(subscribable: Subscribable<T>): Observable<T>;
//# sourceMappingURL=fromSubscribable.d.ts.map