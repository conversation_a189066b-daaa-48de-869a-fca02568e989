{"version": 3, "file": "WebSocketSubject.d.ts", "sourceRoot": "", "sources": ["../../../../../src/internal/observable/dom/WebSocketSubject.ts"], "names": [], "mappings": "AAAA,OAAO,EAAW,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAE1D,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAE9C,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAE1C,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAErD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgGG;AACH,MAAM,WAAW,sBAAsB,CAAC,CAAC;IACvC,iDAAiD;IACjD,GAAG,EAAE,MAAM,CAAC;IACZ,qCAAqC;IACrC,QAAQ,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAClC,2EAA2E;IAC3E,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,CAAC;IACxC;;;OAGG;IACH,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,gBAAgB,CAAC;IAC5C;;;OAGG;IACH,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,CAAC;IACtC;;OAEG;IACH,YAAY,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;IACnC;;OAEG;IACH,aAAa,CAAC,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;IACzC;;;OAGG;IACH,eAAe,CAAC,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;IACrC;;;;OAIG;IACH,aAAa,CAAC,EAAE;QAAE,KAAK,GAAG,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,SAAS,CAAA;KAAE,CAAC;IAChF,kEAAkE;IAClE,UAAU,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC;CACrC;AAWD,oBAAY,gBAAgB,GAAG,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,eAAe,CAAC;AAE7E,qBAAa,gBAAgB,CAAC,CAAC,CAAE,SAAQ,gBAAgB,CAAC,CAAC,CAAC;IAE1D,OAAO,CAAC,OAAO,CAA4B;IAM3C,OAAO,CAAC,OAAO,CAA0B;gBAE7B,iBAAiB,EAAE,MAAM,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;IA2B5G,oGAAoG;IACpG,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;IAOtD,OAAO,CAAC,WAAW;IAQnB;;;;;;;;;;;;;;;;;OAiBG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,QAAQ,EAAE,MAAM,GAAG,EAAE,aAAa,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO;IAkCtF,OAAO,CAAC,cAAc;IA+HtB,WAAW;CAQZ"}