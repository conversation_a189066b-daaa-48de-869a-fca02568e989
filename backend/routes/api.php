<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\DesignController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\MockDataController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::prefix('v1')->group(function () {
    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('register', [AuthController::class, 'register']);
        Route::post('login', [AuthController::class, 'login']);
        
        // Protected auth routes
        Route::middleware('auth:sanctum')->group(function () {
            Route::get('me', [AuthController::class, 'me']);
            Route::post('logout', [AuthController::class, 'logout']);
            Route::put('profile', [AuthController::class, 'updateProfile']);
        });
    });

    // Public product routes (using mock data)
    Route::get('products', [MockDataController::class, 'products']);
    Route::get('products/{id}', [MockDataController::class, 'product']);

    // Public design routes (using mock data)
    Route::get('designs', [MockDataController::class, 'featuredDesigns']);
    Route::get('designs/featured', [MockDataController::class, 'featuredDesigns']);
    Route::get('designs/{id}', [DesignController::class, 'show']);

    // Categories routes (using mock data)
    Route::get('categories', [MockDataController::class, 'categories']);

    // Protected routes
    Route::middleware('auth:sanctum')->group(function () {
        // User design routes
        Route::prefix('designs')->group(function () {
            Route::get('my', [DesignController::class, 'myDesigns']);
            Route::post('/', [DesignController::class, 'store']);
            Route::put('{id}', [DesignController::class, 'update']);
            Route::delete('{id}', [DesignController::class, 'destroy']);
        });

        // Order routes
        Route::apiResource('orders', OrderController::class);
        Route::get('orders/{id}/download', [OrderController::class, 'downloadDesigns']);

        // Admin routes
        Route::middleware('admin')->prefix('admin')->group(function () {
            Route::apiResource('products', ProductController::class)->except(['index', 'show']);
            Route::apiResource('categories', CategoryController::class);
            Route::get('dashboard/stats', function () {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'total_users' => \App\Models\User::count(),
                        'total_products' => \App\Models\Product::count(),
                        'total_designs' => \App\Models\Design::count(),
                        'total_orders' => \App\Models\Order::count(),
                        'pending_orders' => \App\Models\Order::where('status', 'pending')->count(),
                    ]
                ]);
            });
        });
    });
});

// Health check
Route::get('health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now(),
        'service' => 'Printily API'
    ]);
});
