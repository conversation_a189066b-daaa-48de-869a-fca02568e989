<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\DesignController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\MockDataController;
use App\Http\Controllers\Api\MockAuthController;
use App\Http\Controllers\Api\AdminAuthController;
use App\Http\Controllers\Api\AdminProductController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::prefix('v1')->group(function () {
    // Authentication routes (using mock data)
    Route::prefix('auth')->group(function () {
        Route::post('register', [AuthController::class, 'register']);
        Route::post('login', [AuthController::class, 'login']);

        // Protected auth routes (mock)
        Route::post('logout', [MockAuthController::class, 'logout']);
        Route::get('me', [MockAuthController::class, 'profile']);
        Route::put('profile', [MockAuthController::class, 'updateProfile']);
        Route::post('change-password', [MockAuthController::class, 'changePassword']);
    });

    // Public product routes (using mock data)
    Route::get('products', [MockDataController::class, 'products']);
    Route::get('products/{id}', [MockDataController::class, 'product']);

    // Public design routes (using mock data)
    Route::get('designs', [MockDataController::class, 'featuredDesigns']);
    Route::get('designs/featured', [MockDataController::class, 'featuredDesigns']);
    Route::get('designs/{id}', [DesignController::class, 'show']);

    // Categories routes (using mock data)
    Route::get('categories', [MockDataController::class, 'categories']);

    // Protected routes
    Route::middleware('auth:sanctum')->group(function () {
        // User design routes
        Route::prefix('designs')->group(function () {
            Route::get('my', [DesignController::class, 'myDesigns']);
            Route::post('/', [DesignController::class, 'store']);
            Route::put('{id}', [DesignController::class, 'update']);
            Route::delete('{id}', [DesignController::class, 'destroy']);
        });

        // Order routes
        Route::apiResource('orders', OrderController::class);
        Route::get('orders/{id}/download', [OrderController::class, 'downloadDesigns']);

    });

    // Admin routes (separate from protected user routes)
    Route::prefix('admin')->group(function () {
        // Admin authentication (no middleware required)
        Route::post('login', [AdminAuthController::class, 'login']);

        // Protected admin routes (mock auth for demo)
        Route::get('profile', [AdminAuthController::class, 'profile']);
        Route::post('logout', [AdminAuthController::class, 'logout']);
        Route::get('dashboard/stats', [AdminAuthController::class, 'dashboardStats']);

        // Admin product management
        Route::apiResource('products', AdminProductController::class);
        Route::post('products/{id}/toggle-status', [AdminProductController::class, 'toggleStatus']);
        Route::post('products/bulk-action', [AdminProductController::class, 'bulkAction']);
    });
});

// Health check
Route::get('health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now(),
        'service' => 'Printily API'
    ]);
});
