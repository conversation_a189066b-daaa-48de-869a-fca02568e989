<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tshirtCategory = Category::where('slug', 't-shirts')->first();
        $hoodieCategory = Category::where('slug', 'hoodies')->first();
        $mugCategory = Category::where('slug', 'mugs')->first();

        $products = [
            [
                'name' => 'Classic Cotton T-Shirt',
                'slug' => 'classic-cotton-t-shirt',
                'description' => 'Comfortable 100% cotton t-shirt perfect for custom designs',
                'base_price' => 1500.00, // Price in DZD (Algerian Dinar)
                'category_id' => $tshirtCategory->id,
                'mockup_image' => 'mockups/tshirt-classic.png',
                'mockup_positions' => [
                    'front' => ['x' => 150, 'y' => 100, 'width' => 200, 'height' => 250],
                    'back' => ['x' => 150, 'y' => 100, 'width' => 200, 'height' => 250]
                ],
                'available_sizes' => ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
                'available_colors' => [
                    ['name' => 'White', 'hex' => '#FFFFFF'],
                    ['name' => 'Black', 'hex' => '#000000'],
                    ['name' => 'Navy', 'hex' => '#001f3f'],
                    ['name' => 'Red', 'hex' => '#FF4136']
                ],
                'sort_order' => 1,
            ],
            [
                'name' => 'Premium Hoodie',
                'slug' => 'premium-hoodie',
                'description' => 'Warm and comfortable hoodie with kangaroo pocket',
                'base_price' => 4500.00,
                'category_id' => $hoodieCategory->id,
                'mockup_image' => 'mockups/hoodie-premium.png',
                'mockup_positions' => [
                    'front' => ['x' => 150, 'y' => 120, 'width' => 180, 'height' => 220],
                    'back' => ['x' => 150, 'y' => 120, 'width' => 180, 'height' => 220]
                ],
                'available_sizes' => ['S', 'M', 'L', 'XL', 'XXL'],
                'available_colors' => [
                    ['name' => 'Black', 'hex' => '#000000'],
                    ['name' => 'Gray', 'hex' => '#808080'],
                    ['name' => 'Navy', 'hex' => '#001f3f']
                ],
                'sort_order' => 2,
            ],
            [
                'name' => 'Ceramic Coffee Mug',
                'slug' => 'ceramic-coffee-mug',
                'description' => '11oz ceramic mug perfect for morning coffee',
                'base_price' => 800.00,
                'category_id' => $mugCategory->id,
                'mockup_image' => 'mockups/mug-ceramic.png',
                'mockup_positions' => [
                    'wrap' => ['x' => 50, 'y' => 80, 'width' => 250, 'height' => 120]
                ],
                'available_sizes' => ['11oz'],
                'available_colors' => [
                    ['name' => 'White', 'hex' => '#FFFFFF'],
                    ['name' => 'Black', 'hex' => '#000000']
                ],
                'sort_order' => 3,
            ],
        ];

        foreach ($products as $product) {
            Product::create($product);
        }
    }
}
