<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'T-Shirts',
                'slug' => 't-shirts',
                'description' => 'Comfortable cotton t-shirts for custom designs',
                'sort_order' => 1,
            ],
            [
                'name' => 'Hoodies',
                'slug' => 'hoodies',
                'description' => 'Warm hoodies perfect for custom prints',
                'sort_order' => 2,
            ],
            [
                'name' => 'Mugs',
                'slug' => 'mugs',
                'description' => 'Ceramic mugs for personalized designs',
                'sort_order' => 3,
            ],
            [
                'name' => 'Phone Cases',
                'slug' => 'phone-cases',
                'description' => 'Protective phone cases with custom designs',
                'sort_order' => 4,
            ],
            [
                'name' => 'Posters',
                'slug' => 'posters',
                'description' => 'High-quality posters for wall art',
                'sort_order' => 5,
            ],
            [
                'name' => 'Stickers',
                'slug' => 'stickers',
                'description' => 'Vinyl stickers for laptops and more',
                'sort_order' => 6,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
