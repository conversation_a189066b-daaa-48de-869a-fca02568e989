# 🚀 Printily Deployment Guide

## 🎯 Deployment Options

### 1. **Quick Docker Deployment** (Recommended)
- ✅ **Easiest setup** - One command deployment
- ✅ **All services included** - Frontend, Backend, Database, Nginx
- ✅ **Production ready** - Optimized containers
- ✅ **Cross-platform** - Works on any system with Docker

### 2. **Manual Server Deployment**
- ✅ **Full control** - Custom server configuration
- ✅ **Performance optimized** - Direct server deployment
- ✅ **Scalable** - Easy to scale individual components

### 3. **Cloud Deployment**
- ✅ **Heroku, DigitalOcean, AWS** - Cloud platform ready
- ✅ **Auto-scaling** - Handle traffic spikes
- ✅ **Managed services** - Database and storage included

---

## 🐳 **Option 1: Docker Deployment (Recommended)**

### Prerequisites
- **Docker**: 20.10+ installed
- **Docker Compose**: 2.0+ installed
- **Git**: For cloning the repository
- **4GB RAM**: Minimum for all containers

### **Quick Start (5 minutes)**

1. **Clone the repository**
```bash
git clone https://github.com/nadjib-dbz/printily.git
cd printily
```

2. **Run deployment script**
```bash
./deploy.sh
```

3. **Access your application**
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **Full App**: http://localhost (via Nginx)

### **What the deployment includes:**
- ✅ **Laravel Backend** - API server with mock data
- ✅ **Vue.js Frontend** - Complete user interface
- ✅ **Nginx Reverse Proxy** - Load balancing and SSL ready
- ✅ **MySQL Database** - Production database (optional)
- ✅ **SSL Ready** - HTTPS configuration included
- ✅ **Auto-restart** - Containers restart automatically

### **Management Commands**
```bash
# View logs
docker-compose logs -f

# Stop all services
docker-compose down

# Restart services
docker-compose restart

# Update deployment
git pull && ./deploy.sh

# Scale services
docker-compose up -d --scale backend=3
```

---

## 🖥️ **Option 2: Manual Server Deployment**

### Prerequisites
- **Server**: Linux server (Ubuntu 20.04+ recommended)
- **Web Server**: Nginx or Apache
- **PHP**: 8.1+ with required extensions
- **Database**: MySQL 8.0+ (optional - SQLite works)
- **Node.js**: 18+ for frontend build
- **SSL Certificate**: For HTTPS (Let's Encrypt recommended)

### 1. Install Required Software

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install PHP and extensions
sudo apt install php8.1 php8.1-fpm php8.1-mysql php8.1-xml php8.1-curl php8.1-zip php8.1-mbstring php8.1-gd php8.1-sqlite3

# Install Nginx
sudo apt install nginx

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### 2. Database Setup

```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE printily;
CREATE USER 'printily_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON printily.* TO 'printily_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

## 📁 Application Deployment

### 1. Clone and Setup Backend

```bash
# Clone repository
git clone <your-repo-url> /var/www/printily
cd /var/www/printily

# Setup Laravel backend
cd backend
composer install --optimize-autoloader --no-dev

# Configure environment
cp .env.example .env
nano .env
```

**Update .env file:**
```env
APP_NAME=Printily
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=printily
DB_USERNAME=printily_user
DB_PASSWORD=secure_password

# Add other production settings
```

```bash
# Generate application key
php artisan key:generate

# Run migrations and seeders
php artisan migrate --force
php artisan db:seed --force

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set permissions
sudo chown -R www-data:www-data /var/www/printily
sudo chmod -R 755 /var/www/printily
sudo chmod -R 775 /var/www/printily/backend/storage
sudo chmod -R 775 /var/www/printily/backend/bootstrap/cache
```

### 2. Build and Deploy Frontend

```bash
cd /var/www/printily/frontend

# Install dependencies
npm install

# Update environment for production
echo "VITE_API_URL=https://yourdomain.com/api/v1" > .env

# Build for production
npm run build

# Move build files to web root
sudo mkdir -p /var/www/printily/public
sudo cp -r dist/* /var/www/printily/public/
```

## 🌐 Web Server Configuration

### Nginx Configuration

Create `/etc/nginx/sites-available/printily`:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    root /var/www/printily/public;
    index index.html index.php;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Frontend routes (Vue.js SPA)
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API routes (Laravel)
    location /api {
        try_files $uri $uri/ /backend/public/index.php?$query_string;
    }

    # Laravel backend
    location ~ ^/backend/(.*)$ {
        alias /var/www/printily/backend/public/$1;
        try_files $uri $uri/ @laravel;

        location ~ \.php$ {
            include fastcgi_params;
            fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
            fastcgi_param SCRIPT_FILENAME $request_filename;
        }
    }

    location @laravel {
        rewrite /backend/(.*)$ /backend/public/index.php?/$1 last;
    }

    # Static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security
    location ~ /\.ht {
        deny all;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/printily /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔒 SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoring & Maintenance

### 1. Log Management

```bash
# Laravel logs
tail -f /var/www/printily/backend/storage/logs/laravel.log

# Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 2. Backup Script

Create `/home/<USER>/printily-backup.sh`:

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/printily"

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u printily_user -p'secure_password' printily > $BACKUP_DIR/database_$DATE.sql

# Files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /var/www/printily

# Keep only last 7 days
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

Make executable and add to cron:
```bash
chmod +x /home/<USER>/printily-backup.sh
sudo crontab -e
# Add: 0 2 * * * /home/<USER>/printily-backup.sh
```

## 🔄 Updates & Maintenance

### Application Updates

```bash
cd /var/www/printily

# Pull latest changes
git pull origin main

# Update backend
cd backend
composer install --optimize-autoloader --no-dev
php artisan migrate --force
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Update frontend
cd ../frontend
npm install
npm run build
sudo cp -r dist/* /var/www/printily/public/

# Restart services
sudo systemctl reload nginx
sudo systemctl reload php8.1-fpm
```

## 🚨 Troubleshooting

### Common Issues

1. **Permission errors**: Check file ownership and permissions
2. **Database connection**: Verify credentials and MySQL service
3. **API not working**: Check Nginx configuration and PHP-FPM
4. **Frontend not loading**: Verify build files and Nginx config

### Health Checks

```bash
# Check services
sudo systemctl status nginx
sudo systemctl status php8.1-fpm
sudo systemctl status mysql

# Check logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/www/printily/backend/storage/logs/laravel.log
```

## 📈 Performance Optimization

1. **Enable OPcache** in PHP configuration
2. **Configure Redis** for session and cache storage
3. **Use CDN** for static assets
4. **Enable Gzip** compression in Nginx
5. **Optimize database** queries and indexes

Your Printily platform is now ready for production! 🎉
