version: '3.8'

services:
  # Laravel Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: printily_backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/var/www/html
      - backend_storage:/var/www/html/storage
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_KEY=base64:your-app-key-here
      - DB_CONNECTION=sqlite
      - SESSION_DRIVER=file
      - CACHE_STORE=file
    networks:
      - printily_network
    depends_on:
      - database

  # Vue.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: printily_frontend
    ports:
      - "3000:80"
    environment:
      - VITE_API_URL=http://localhost:8000/api/v1
    networks:
      - printily_network
    depends_on:
      - backend

  # Database (SQLite file-based, but MySQL option available)
  database:
    image: mysql:8.0
    container_name: printily_database
    environment:
      MYSQL_ROOT_PASSWORD: printily_root_password
      MYSQL_DATABASE: printily
      MY<PERSON><PERSON>_USER: printily_user
      MYSQL_PASSWORD: printily_password
    volumes:
      - database_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - printily_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: printily_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    networks:
      - printily_network
    depends_on:
      - frontend
      - backend

volumes:
  database_data:
  backend_storage:

networks:
  printily_network:
    driver: bridge
